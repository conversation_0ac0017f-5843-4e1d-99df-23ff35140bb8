// api/doctorApis.ts
import axios from 'axios';

// types/Doctor.ts

export interface Address {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface DoctorDTO { // This should represent the data received from the backend
  staffId?: string; // Assuming staffId is part of the DTO
  firstName: string;
  lastName: string;
  middleName?: string | null;
  specializations: string[]; // Keeping as array for frontend
  roleType: string;
  qualification?: string | null; // Making nullable to match backend
  gender: string;
  dateOfBirth: string; // LocalDate from backend is often represented as string
  age: string;
  mobileNumber?: string | null; // Making nullable to match backend
  email?: string | null; // Making nullable to match backend
  registrationNumber?: string | null; // Making nullable to match backend
  registrationState?: string | null;
  // --- CHANGE TO STRING ---
 yearsOfExperience: number;
 // To match backend's Int? and frontend's string handling
  // -------------------------
  telemedicineReady?: boolean | null; // Making nullable to match backend
  languagesSpoken: string[]; // Keeping as array for frontend
  isActive: boolean;
  address: Address[]; // Backend uses List, so array is fine here
  createdAt?: string; // LocalDateTime from backend is often represented as string
  updatedAt?: string; // LocalDateTime from backend is often represented as string
  primarySpecialization?: string | null; // Making nullable to match backend
  facilityId: string; // Assuming this is part of the DTO
}

export interface DoctorForm { // This represents the state managed in the React form
  firstName: string;
  lastName: string;
  middleName?: string | null;
  specializations: string[];
  roleType: string;
  qualification?: string | null;
  gender: string;
  dateOfBirth: string;
  mobileNumber?: string | null;
  email?: string | null;
  registrationNumber?: string | null;
  registrationState?: string | null;
  // --- KEEP AS STRING ---
  yearsOfExperience: string; // Must be string for form input binding
  // ----------------------
  telemedicineReady?: boolean | null;
  languagesSpoken: string[];
  isActive: boolean;
  address: Address[]; // Array of addresses
  primarySpecialization?: string | null;
  // No facilityId here, it's handled externally
}

export interface ApiResponseDoctorDTO {
  success: boolean;
  data: DoctorDTO;
  message?: string;
  error?: string;
}

export interface ApiResponseDoctorList {
  success: boolean;
  data: DoctorDTO[];
  message?: string;
  error?: string;
}


const BASE_URL = import.meta.env.VITE_API_BASE_URL;

axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
  if (token) config.headers.Authorization = `Bearer ${token}`;

  const facilityId = localStorage.getItem('selectedFacilityId');
  if (facilityId) config.headers['X-Facility-Id'] = facilityId;

  return config;
});

// GET all doctors
export const getAllDoctors = async (): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/staff`);
    return { success: true, data: res.data };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message,
    };
  }
};

// GET active doctors
export const getActiveDoctors = async (): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/staff/active`);
    return { success: true, data: res.data };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message,
    };
  }
};

// GET by ID
export const getDoctorById = async (id: string): Promise<ApiResponseDoctorDTO> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/staff/${id}`);
    // Ensure the data from the backend matches DoctorDTO structure
    return { success: true, data: res.data };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO, // Return empty DTO on error
      error: err.response?.data?.message || err.message,
    };
  }
};

// GET by specialization
export const getDoctorsBySpecialization = async (specId: string): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/staff/specialization/${specId}`);
    return { success: true, data: res.data };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message,
    };
  }
};

// POST create doctor
export const createDoctor = async (payload: DoctorDTO): Promise<ApiResponseDoctorDTO> => {
  try {
    const apiPayload = {
      ...payload,
      yearsOfExperience:
        payload.yearsOfExperience !== 0 ? payload.yearsOfExperience : null,
      middleName: payload.middleName || null,
      qualification: payload.qualification || null,
      mobileNumber: payload.mobileNumber || null,
      email: payload.email || null,
      registrationNumber: payload.registrationNumber || null,
      registrationState: payload.registrationState || null,
      telemedicineReady: payload.telemedicineReady ?? null,
      primarySpecialization: payload.primarySpecialization || null,
      specializations: payload.specializations.length > 0 ? payload.specializations : [],
      languagesSpoken: payload.languagesSpoken.length > 0 ? payload.languagesSpoken : [],
      address: payload.address || [],
    };

    const res = await axios.post(`${BASE_URL}/api/staff`, apiPayload);
    return { success: true, data: res.data, message: 'Doctor created successfully' };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO,
      error: err.response?.data?.message || err.message,
    };
  }
};

// PUT update doctor
export const updateDoctor = async (
  id: string,
  payload: DoctorDTO
): Promise<ApiResponseDoctorDTO> => {
  try {
    const apiPayload = {
      ...payload,
      yearsOfExperience:
        payload.yearsOfExperience !== null && payload.yearsOfExperience !== undefined
          ? payload.yearsOfExperience
          : null,
      middleName: payload.middleName || null,
      qualification: payload.qualification || null,
      mobileNumber: payload.mobileNumber || null,
      email: payload.email || null,
      registrationNumber: payload.registrationNumber || null,
      registrationState: payload.registrationState || null,
      telemedicineReady: payload.telemedicineReady ?? null,
      primarySpecialization: payload.primarySpecialization || null,
      specializations: payload.specializations.length > 0 ? payload.specializations : [],
      languagesSpoken: payload.languagesSpoken.length > 0 ? payload.languagesSpoken : [],
      address: payload.address || [],
    };

    const res = await axios.put(`${BASE_URL}/api/staff/${id}`, apiPayload);
    return { success: true, data: res.data, message: 'Doctor updated successfully' };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO,
      error: err.response?.data?.message || err.message,
    };
  }
};
// DELETE doctor
export const deleteDoctor = async (
  id: string
): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    await axios.delete(`${BASE_URL}/api/staff/${id}`);
    return { success: true, message: 'Doctor deleted successfully' };
  } catch (err: any) {
    return {
      success: false,
      error: err.response?.data?.message || err.message,
    };
  }
};