// ScheduleForm.tsx

import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  getDoctors
} from "../../services/scheduleApis";
import {
  getConsultantSlotConfigs,
  createConsultantSlotConfig,
  updateConsultantSlotConfig
} from "../../services/consultantslot-config";
import { Button } from "../../commonfields/Button";
import { Input } from "../../commonfields/Input";
import { Select } from "../../commonfields/Select";
import { Calendar as CalendarInput } from "../../commonfields/Calendar";
import { FormField } from "../../commonfields/FormField";
import { showSuccess, showError } from "../../utils/toastUtils";
import { X } from "lucide-react";
import { dayOfWeekOptions, slotDurationOptions } from "../../types/appointmentenums";
import type { ConsultantSlotConfigDto } from "../../services/consultantslot-config";
import type { Doctor } from "../../services/scheduleApis";

const ScheduleForm: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const doctorId = searchParams.get("doctorId");
  const mode = searchParams.get("mode"); // 'edit' or 'create'

  const [formData, setFormData] = useState<ConsultantSlotConfigDto>({
    consultantId: "", // Will be set after loading doctor data
    daysOfWeek: [],
    startTime: "09:00",
    endTime: "17:00",
    slotDuration: 30,
    effectiveFrom: new Date().toISOString().split("T")[0],
    effectiveTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
  });

  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!doctorId) {
      showError("No doctor ID provided");
      navigate("/doctors");
      return;
    }

    const fetchInitialData = async () => {
      try {
        const doctorRes = await getDoctors();

        if (doctorRes.success && doctorRes.data) {
          setDoctors(doctorRes.data);

          // Find the current doctor and set the correct consultantId
          const currentDoctor = doctorRes.data.find(d => d.doctorId === doctorId);
          if (currentDoctor) {
            const consultantId = currentDoctor.consultantId || currentDoctor.doctorId;

            // Update formData with the correct consultantId
            setFormData(prev => ({
              ...prev,
              consultantId: consultantId
            }));

            // Load schedule data using the correct consultantId
            const scheduleRes = await getConsultantSlotConfigs(consultantId);

            if (mode === "edit" && scheduleRes.length > 0) {
              const schedule = scheduleRes[0];
              setFormData({
                consultantId: schedule.consultantId,
                daysOfWeek: schedule.daysOfWeek || [],
                startTime: schedule.startTime,
                endTime: schedule.endTime,
                slotDuration: schedule.slotDuration,
                effectiveFrom: schedule.effectiveFrom,
                effectiveTo: schedule.effectiveTo || ""
              });
            } else if (mode === "edit") {
              showError("No existing schedule found.");
            }
          } else {
            showError("Doctor not found.");
          }
        }
      } catch (error) {
        console.error(error);
        showError("Failed to load doctor or schedule data.");
      }
    };

    fetchInitialData();
  }, [doctorId, mode, navigate]);

  const handleDayToggle = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: checked
        ? [...prev.daysOfWeek, day]
        : prev.daysOfWeek.filter(d => d !== day)
    }));
  };

  const handleSelectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: dayOfWeekOptions.map(day => day.toUpperCase())
    }));
  };

  const handleDeselectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: []
    }));
  };

 const handleCreateSchedule = async () => {
  try {
    setLoading(true);
    // Use the consultantId from formData instead of doctorId
    const res = await createConsultantSlotConfig(formData.consultantId, formData);
    if (res) {
      navigate("/doctors", { state: { toast: "Schedule created successfully." } });
    } else {
      showError("Failed to create schedule.");
    }
  } catch (err: any) {
    console.error("Error while creating schedule:", err);

   if (err.response?.status === 409) {
  navigate("/doctors", {
    state: { toast: "A schedule already exists for this practitioner. Please edit it instead.", toastType: "error" },
  });
}
 else {
      showError("An unexpected error occurred while creating schedule.");
    }
  } finally {
    setLoading(false);
  }
};


  const handleUpdateSchedule = async () => {
    try {
      setLoading(true);
      // Use the consultantId from formData instead of doctorId
      const res = await updateConsultantSlotConfig(formData.consultantId, formData);
      if (res) {
        navigate("/doctors", { state: { toast: "Schedule updated successfully." } });
      } else {
        showError("Failed to update schedule.");
      }
    } catch (err) {
      console.error("Error while updating schedule:", err);
      showError("An unexpected error occurred while updating schedule.");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!doctorId) {
      showError("Missing doctorId");
      return;
    }
    if (formData.daysOfWeek.length === 0) {
      showError("Select at least one day of the week.");
      return;
    }

    if (mode === "edit") {
      await handleUpdateSchedule();
    } else {
      await handleCreateSchedule();
    }
  };

  return (
    <div className="fixed inset-0 backdrop-blur-sm bg-white/30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {mode === "edit" ? "Edit Schedule" : "Create Schedule"}
          </h2>
          <Button onClick={() => navigate("/doctors")} className="p-2 text-gray-400 hover:text-gray-600">
            <X size={20} />
          </Button>
        </div>

        <form onSubmit={onSubmit} className="space-y-6">
          <FormField label="Doctor/Consultant" required>
            <Select value={doctorId || ""} disabled>
              {doctors.map(doc => (
                <option key={doc.doctorId} value={doc.doctorId}>
                  {doc.fullName}
                </option>
              ))}
            </Select>
          </FormField>

          <FormField label="Days of Week" required>
            <div className="flex gap-3 mb-3">
              <Button type="button" onClick={handleSelectAllDays} className="bg-indigo-600 text-white px-3 py-1 text-sm rounded">Select All</Button>
              <Button type="button" onClick={handleDeselectAllDays} className="bg-gray-500 text-white px-3 py-1 text-sm rounded">Deselect All</Button>
              <span className="text-sm text-gray-500">{formData.daysOfWeek.length} of 7 selected</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {dayOfWeekOptions.map(day => (
                <label key={day} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.daysOfWeek.includes(day.toUpperCase())}
                    onChange={e => handleDayToggle(day.toUpperCase(), e.target.checked)}
                    className="form-checkbox h-4 w-4"
                  />
                  {day}
                </label>
              ))}
            </div>
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Start Time" required>
              <Input
                type="time"
                value={formData.startTime}
                onChange={e => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
              />
            </FormField>
            <FormField label="End Time" required>
              <Input
                type="time"
                value={formData.endTime}
                onChange={e => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
              />
            </FormField>
          </div>

          <FormField label="Slot Duration" required>
            <Select
              value={formData.slotDuration.toString()}
              onChange={e => setFormData(prev => ({ ...prev, slotDuration: parseInt(e.target.value) }))}
            >
              {slotDurationOptions.map(d => (
                <option key={d} value={d}>{d} minutes</option>
              ))}
            </Select>
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Effective From" required>
              <CalendarInput
                value={formData.effectiveFrom}
                onChange={e => setFormData(prev => ({ ...prev, effectiveFrom: e.target.value }))}
              />
            </FormField>
            <FormField label="Effective To" required>
              <CalendarInput
                value={formData.effectiveTo}
                onChange={e => setFormData(prev => ({ ...prev, effectiveTo: e.target.value }))}
              />
            </FormField>
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" onClick={() => navigate("/doctors")} className="bg-gray-300 px-5 py-2 rounded">Cancel</Button>
            <Button type="submit" disabled={loading} className="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">
              {loading ? (mode === "edit" ? "Updating..." : "Creating...") : (mode === "edit" ? "Update Schedule" : "Create Schedule")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
export default ScheduleForm;
