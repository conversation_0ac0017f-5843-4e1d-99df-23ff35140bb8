import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getScheduleConfigs } from "../../services/scheduleApis";
import { X } from "lucide-react";

// Format date to DD-MM-YYYY
const formatDate = (dateStr: string) => {
  const options: Intl.DateTimeFormatOptions = { day: "2-digit", month: "2-digit", year: "numeric" };
  return new Date(dateStr).toLocaleDateString("en-IN", options);
};

// Format time to 12-hour with AM/PM
const formatTime = (timeStr: string) => {
  const [hour, minute] = timeStr.split(":");
  const date = new Date();
  date.setHours(Number(hour), Number(minute));
  return date.toLocaleTimeString("en-IN", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

// Capitalize each day
const formatDays = (days: string[]) => {
  return days
    .map((day) => day.charAt(0).toUpperCase() + day.slice(1).toLowerCase())
    .join(", ");
};

const DoctorSchedulePage = () => {
  const { doctorId } = useParams();
  const navigate = useNavigate();
  const [scheduleConfigs, setScheduleConfigs] = useState([]);

  useEffect(() => {
    const fetchSchedule = async () => {
      if (!doctorId) return;
      const res = await getScheduleConfigs({ consultantId: doctorId });

      const filtered = (res?.results || []).filter(
        (config) => config.consultantId === doctorId
      );
      setScheduleConfigs(filtered);
    };
    fetchSchedule();
  }, [doctorId]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="flex justify-between items-center max-w-5xl mx-auto mb-6">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-500 bg-clip-text text-transparent">
          Doctor Schedule Configuration
        </h2>
        <button
          onClick={() => navigate("/doctors")}
          className="text-gray-600 hover:text-red-500 transition"
        >
          <X size={28} />
        </button>
      </div>

      {/* Schedule Cards */}
      <div className="max-w-5xl mx-auto space-y-6">
        {scheduleConfigs.length === 0 ? (
          <div className="text-center text-gray-500 text-lg">
            No schedule configuration found for this doctor.
          </div>
        ) : (
          scheduleConfigs.map((config, index) => (
            <div
              key={index}
              className="bg-white border shadow rounded-xl p-6"
            >
              <div className="mb-2">
                <span className="font-semibold text-gray-700">Available Days:</span>{" "}
                <span className="text-purple-600">{formatDays(config.daysOfWeek)}</span>
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-700">Timings:</span>{" "}
                <span>{formatTime(config.startTime)} - {formatTime(config.endTime)}</span>
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-700">Slot Duration:</span>{" "}
                <span>{config.slotDuration} minutes</span>
              </div>
              <div>
                <span className="font-semibold text-gray-700">Effective Period:</span>{" "}
                <span>{formatDate(config.effectiveFrom)} to {formatDate(config.effectiveTo)}</span>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer Button */}
      <div className="max-w-5xl mx-auto mt-10 text-center">
        <button
          onClick={() => navigate("/doctors")}
          className="bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-lg shadow hover:opacity-90 transition"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default DoctorSchedulePage;
