import { z } from "zod";
import {
  IdentifierType, AddressType,
  ContactMode,
  PhonePref,
} from "../../types/patientenums";

// --- Reusable Validators ---


const currentYear = new Date().getFullYear();


const requiredDropdown = (msg: string) =>
  z.union([z.string(), z.literal(null)])
    .transform((val) => val ?? "")
    .refine((val) => val.length > 0, { message: msg });

const requiredName = (msg: string) =>
  z.union([z.string(), z.literal(null)])
    .transform((val) => val ?? "")
    .refine((val) => /^[A-Za-z][A-Za-z\s'-]*$/.test(val), { message: "Only letters, spaces, apostrophes (') and hyphens (-) allowed and first character to be char" })
    .refine((val) => val.length > 0, { message: msg })
    .refine((val) => val.length <= 31, { message: "Name must not exceed 30 characters" });

const optionalName = () =>
  z.union([z.string(), z.literal(null)])
    .optional()
    .transform((val) => val ?? "")
    .refine((val) => !val || /^[A-Za-z\s'-]+$/.test(val), {
      message: "Only letters, spaces, apostrophes (') and hyphens (-) allowed"
    })
    .refine((val) => !val || val.length <= 31, {
      message: "Name must not exceed 30 characters",
    });

const optionalPhone = () =>
  z.preprocess(
    (val) => (val === null || val === undefined ? "" : val),
    z.string().refine(
      (val) => val === "" || /^\d{10}$/.test(val),
      { message: "Mobile must be 10 digits" }
    )
  );


const optionalEmail = () =>
  z.preprocess(
    (val) => (val === null || val === undefined ? "" : val),
    z.string().refine(
      (val) => val === "" || /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(val),
      {
        message: "Invalid email format",
      }
    )
  );


// --- Contact Schema ---

const contactSchema = z.object({
  mobileNumber: optionalPhone(),
  phoneNumber: optionalPhone(),
  email: optionalEmail(),
  preferredContactMode: z.nativeEnum(ContactMode).nullable().optional(),
  phoneContactPreference: z.nativeEnum(PhonePref).nullable().optional(),
  consentToShare: z.boolean().optional(),
});

const contactsSchema = z.array(contactSchema).superRefine((contacts, ctx) => {
  contacts.forEach((contact, index) => {
    const phone = (contact.phoneNumber ?? "").trim();

    if (index === 0) {
      if (!phone) {
        ctx.addIssue({
          path: [index, "phoneNumber"],
          code: z.ZodIssueCode.custom,
          message: "Primary contact phone number is required",
        });
      } else if (!/^\d{10}$/.test(phone)) {
        ctx.addIssue({
          path: [index, "phoneNumber"],
          code: z.ZodIssueCode.custom,
          message: "Phone number must be 10 digits",
        });
      }
    } else {
      if (phone && !/^\d{10}$/.test(phone)) {
        ctx.addIssue({
          path: [index, "phoneNumber"],
          code: z.ZodIssueCode.custom,
          message: "Phone number must be 10 digits",
        });
      }
    }
  });
});

const addressSchema = z.object({
  addressType: z.nativeEnum(AddressType).optional(),
  houseNoOrFlatNo: z.string().nullable().optional(),
  localityOrSector: z.string().nullable().optional(),
  cityOrVillage: z.string().nullable().optional(),
  pincode: z.string().nullable().optional()
    .refine((val) => {
      if (!val) return true; // allow empty/null
      return /^\d{6}$/.test(val); // must be 6 digits if present
    }, {
      message: "Pincode must be exactly 6 digits",
    }),
  districtId: z.string().nullable().optional(),
  stateId: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
});


const emergencyContactSchema = z.object({
  contactName: z.string().nullable().optional(),
  relationship: z.any().nullable().optional(),
  phoneNumber: z.string().nullable().optional(),
}).superRefine((data, ctx) => {
  const { contactName, relationship, phoneNumber } = data;

  const isAnyFilled =
    (contactName && contactName.trim() !== "") ||
    (relationship !== null && relationship !== undefined && relationship !== "") ||
    (phoneNumber && phoneNumber.trim() !== "");

  if (isAnyFilled) {
    if (!contactName || contactName.trim() === "") {
      ctx.addIssue({
        path: ["contactName"],
        code: z.ZodIssueCode.custom,
        message: "Please enter emergency contact name",
      });
    }

    if (!relationship || relationship === "") {
      ctx.addIssue({
        path: ["relationship"],
        code: z.ZodIssueCode.custom,
        message: "Please select relationship",
      });
    }

    if (!phoneNumber || phoneNumber.trim() === "") {
      ctx.addIssue({
        path: ["phoneNumber"],
        code: z.ZodIssueCode.custom,
        message: "Phone number is required",
      });
    } else if (!/^\d{10}$/.test(phoneNumber)) {
      ctx.addIssue({
        path: ["phoneNumber"],
        code: z.ZodIssueCode.custom,
        message: "Phone number must be exactly 10 digits",
      });
    }
  }
});


const insuranceSchema = z.object({
  policyStartDate: z.string().nullable().optional()
    .refine(val => {
      if (!val) return true;
      const year = Number(val.slice(0, 4));
      return year >= 1900;
    }, { message: "Policy start year must be 1900 or later" }),
  policyEndDate: z.string().nullable().optional()
    .refine(val => {
      if (!val) return true;
      const year = Number(val.slice(0, 4));
      return year <= currentYear + 100;
    }, { message: `Policy end year must be less than or equal to ${currentYear + 100}` }),
}).superRefine((data, ctx) => {
  if (data.policyStartDate && !data.policyEndDate) {
    ctx.addIssue({
      path: ["policyEndDate"],
      code: z.ZodIssueCode.custom,
      message: "Policy end date is required if start date is entered",
    });
  }
  if (data.policyStartDate && data.policyEndDate) {
    const startDate = new Date(data.policyStartDate);
    const endDate = new Date(data.policyEndDate);
    if (endDate <= startDate) {
      ctx.addIssue({
        path: ["policyEndDate"],
        code: z.ZodIssueCode.custom,
        message: "Policy end date must be greater than start date",
      });
    }
  }
});



const requiredString = (msg: string) =>
  z.preprocess(
    (val) => {
      // Accept string or anything else, convert null/undefined/empty to ""
      if (val === null || val === undefined) return "";
      if (typeof val === "string") return val.trim();
      // If not string, convert to empty string (or you can throw here if you want stricter)
      return "";
    },
    z.string().min(1, { message: msg })
  );

const abhaSchema = z.object({
  abhaNumber: z
    .string()
    .nullable()
    .optional()
    .transform((val) => val ?? "")
    .refine((val) => {
      return val === "" || /^\d{2}-\d{4}-\d{4}-\d{4}$/.test(val);
    }, {
      message: "ABHA number must be in the format 12-1234-1234-1234",
    }),
  abhaAddress: z.string().nullable().optional(),
});

const requiredDateOfBirth = (msg: string) =>
  z.union([z.string(), z.literal(null)])
    .transform((val) => val ?? "")
    .refine((val) => val.length > 0, { message: msg })
    .refine((val) => {
      if (!val) return true;
      const date = new Date(val);
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      return date <= today;
    }, { message: "Date of birth cannot be in the future" });

const optionalAge = () =>
  z
    .union([z.string(), z.literal(null)])
    .transform((val) => val?.trim() ?? "")
    .refine(
      (val) => !val || /^\d{1,3}Y\s\d{1,2}M\s\d{1,2}D$/.test(val),
      { message: "Format must be like '3Y 03M 33D'" }
    )
    .superRefine((val, ctx) => {
      if (!val) return;

      const match = val.match(/^(\d{1,3})Y\s(\d{1,2})M\s(\d{1,2})D$/);
      if (!match) return; // Already handled by refine

      const years = parseInt(match[1], 10);
      const months = parseInt(match[2], 10);
      const days = parseInt(match[3], 10);

      if (years < 0 || years > 150) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Years must be between 0 and 150",
        });
      }

      if (months < 0 || months >= 12) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Months must be between 0 and 11",
        });
      }

      if (days < 0 || days >= 32) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Days must be between 0 and 31",
        });
      }
    });



// --- Patient Schema ---

export const patientSchema = z.object({
  facilityId: requiredString("Facility is required"),

  title: requiredDropdown("Title is required"),

  firstName: requiredName("First name is required"),
  middleName: optionalName(),
  lastName: requiredName("Last name is required"),

  dateOfBirth: requiredDateOfBirth("Date of birth is required"),
  age: optionalAge(),
  gender: requiredDropdown("Gender is required"),

  identifierType: z.union([
    z.nativeEnum(IdentifierType),
    z.literal(""),
    z.null()
  ]).optional(),

  identifierNumber: z.union([z.string(), z.literal(null)])
    .transform((val) => val ?? "")
    .optional(),


  contacts: contactsSchema,
  addresses: z.array(addressSchema).optional(),
  emergencyContacts: z.array(emergencyContactSchema).optional(),
  abha: abhaSchema.optional(),
  insurance: insuranceSchema.optional(),
}).superRefine((data, ctx) => {
  const { identifierType, identifierNumber } = data;

  if (identifierNumber && !identifierType) {
    ctx.addIssue({
      path: ["identifierType"],
      code: z.ZodIssueCode.custom,
      message: "Please select Identifier Type before entering Identifier Number",
    });
  }

  if (identifierType && identifierNumber) {
    const patterns: Record<IdentifierType, { regex: RegExp; message: string }> = {
      // [IdentifierType.ABHA]: {
      //   regex: /^\d{2}-\d{4}-\d{4}-\d{4}$/,
      //   message: "ABHA must be in the format 12-1234-1234-1234",
      // },
      [IdentifierType.Aadhar]: {
        regex: /^(\d{12}|\d{4}\s\d{4}\s\d{4})$/,
        message: "Aadhaar must be 12 digits or in the format 1234 5678 9012",
      },
      [IdentifierType.PAN]: {
        regex: /^[A-Z]{5}[0-9]{4}[A-Z]$/,
        message: "PAN must be in the format **********",
      },
      [IdentifierType.Passport]: {
        regex: /^[A-Z][0-9]{7}$/,
        message: "Passport must be in the format ********",
      },
      [IdentifierType.Driving_License]: {
        regex: /^[A-Z]{2}[0-9]{2}[0-9]{11}$/,
        message: "Driving License must be in the format TS11620210003606",
      },
    };

    const pattern = patterns[identifierType];

    if (!pattern.regex.test(identifierNumber)) {
      ctx.addIssue({
        path: ["identifierNumber"],
        code: z.ZodIssueCode.custom,
        message: pattern.message,
      });
    }
  }
});
