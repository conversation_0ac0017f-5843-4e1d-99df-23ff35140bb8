import React, { useState, useEffect } from "react";
import { defaultPatientRegistrationPayload } from "../../types/patient";
import type { PatientRegistrationPayload } from "../../types/patient";
import { patientSchema } from "../../zod_validations/patient/patientSchema";
import { createPatient, getPatientById, updatePatient } from "../../services/patientApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { useNavigate } from "react-router-dom";
import { usePatientFormStore } from "../../store/patientFormStore";
import { AddressType } from "../../types/patientenums";
import { useFormValidation } from "../../hooks/useFormValidation";
import { createChangeHandler } from "../../utils/updateFieldValueWithValidation";
import isEqual from "lodash.isequal";
import ConfirmDialog from "../../utils/ConfirmDialog";
import { getFreshPatientForm } from "../../utils/patientPayloadResetUtil";



// Sections
import { PatientVerificationSection } from "./sections/PatientVerificationSection";
import { PatientBasicInfoSection } from "./sections/PatientBasicInfoSection";
import { PatientContactAddressSection } from "./sections/PatientContactAddressSection";
import { PatientAdditionalInfoSection } from "./sections/PatientAdditionalInfoSection";
import { TokenModal } from "../token/TokenModal";

type Props = {
    patientId?: string;
};

export const PatientRegistrationForm: React.FC<Props> = ({ patientId }) => {
    const [form, setForm] = useState<PatientRegistrationPayload>(getFreshPatientForm());
    const [presentSameAsPermanent, setPresentSameAsPermanent] = useState(false);
    const { quickFormData, clearQuickFormData } = usePatientFormStore();
    const [showTokenModal, setShowTokenModal] = useState(false);
    const [createdPatient, setCreatedPatient] = useState<any>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [originalForm, setOriginalForm] = useState<PatientRegistrationPayload | null>(null);
    const [showNoChangeModal, setShowNoChangeModal] = useState(false);

    const resetForm = () => {
        setForm(getFreshPatientForm());
        setPresentSameAsPermanent(false);
        clearAllErrors();
    };


    const navigate = useNavigate();

    const {
        errors,
        validateField,
        validateForm,
        clearAllErrors
    } = useFormValidation(patientSchema);

    const allErrors = errors;
    const onFieldChange = createChangeHandler(setForm, validateField);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (isSubmitting) return;
        setIsSubmitting(true);

        const storedFacilityName = localStorage.getItem("Facility");
        if (!storedFacilityName) {
            showError("Missing Facility", "Facility not found in localStorage.");
            setIsSubmitting(false);
            return;
        }

        const facilityNameToIdMap: Record<string, number> = {
            "City Health Center": 1,
            "Green Valley Hospital": 2,
            "Civil Hospital Shillong": 3,
        };

        const facilityIdNumber = facilityNameToIdMap[storedFacilityName];
        if (!facilityIdNumber) {
            showError("Invalid Facility", `No numeric ID found for: ${storedFacilityName}`);
            setIsSubmitting(false);
            return;
        }

        const formWithFacility: PatientRegistrationPayload = {
            ...form,
            facilityId: facilityIdNumber.toString(),
            insurance: {
                ...form.insurance,
                coverageAmount:
                    form.insurance?.coverageAmount !== undefined && form.insurance.coverageAmount !== null && String(form.insurance.coverageAmount) !== ""
                        ? Number(form.insurance.coverageAmount)
                        : undefined,
            },
            annualIncome: form.annualIncome || "",
        };

        const validationErrors = validateForm(formWithFacility);
        console.log("Validation Errors:", validationErrors);
        if (Object.keys(validationErrors).length > 0) {
            showError("Fix errors in form", "Please correct the highlighted errors before submitting.");
            setIsSubmitting(false);
            return;
        }

        clearAllErrors();
        const fullName = `${form.firstName} ${form.middleName || ""} ${form.lastName}`.trim();

        try {
            if (patientId) {
                if (originalForm && isEqual(originalForm, formWithFacility)) {
                    setShowNoChangeModal(true);
                    setIsSubmitting(false);
                    return;
                }
                const { success, error } = await updatePatient(patientId, formWithFacility);
                if (success) {
                    showSuccess("Patient updated successfully", `Patient Name: ${fullName}`);
                    navigate("/list");
                } else {
                    console.log(error)
                    showError("Update Failed", "Error in updating patient.");
                }
            } else {
                const { success, error, data } = await createPatient(formWithFacility);
                if (success) {
                    showSuccess("Patient Created Successfully", `Patient Name: ${fullName}`);
                    // navigate("/list");
                    const now = new Date()
                        .toLocaleString("sv-SE", {
                            timeZone: "Asia/Kolkata",
                            hour12: false,
                            year: "numeric",
                            month: "2-digit",
                            day: "2-digit",
                            hour: "2-digit",
                            minute: "2-digit",
                        })
                        .replace(" ", "T");

                    const addressObj = form.addresses?.[0] || {};
                    const addressParts = [
                        addressObj.houseNoOrFlatNo,
                        addressObj.localityOrSector,
                        addressObj.cityOrVillage,
                        addressObj.districtId,
                        addressObj.stateId,
                        addressObj.pincode,
                        addressObj.country,
                    ].filter(part => part && part !== "N/A");

                    const formattedAddress = addressParts.join(", ");

                    const modalData = {
                        patient_id: data.patientId || "N/A",
                        name: fullName,
                        mobile: form.contacts?.[0]?.phoneNumber || "",
                        date_time: now,
                        gender: form.gender ?? "",
                        age: form.age ?? null,
                        religion: form.religion ?? "",
                        maritalStatus: form.maritalStatus ?? "",
                        address: formattedAddress,
                    };

                    setCreatedPatient(modalData);      // ✅ Set modal data
                    setShowTokenModal(true);           // ✅ Show modal
                    setTimeout(() => {
                        setForm(getFreshPatientForm());  // ✅ Fully reset form state
                        setPresentSameAsPermanent(false);
                        clearAllErrors();
                    }, 0);

                } else {
                    const errorMessage = error || "";
                    if (errorMessage.toLowerCase().includes("duplicate")) {
                        showError("Patient Already Exists", "A similar patient already exists.");
                    } else {
                        showError("Creating Patient Failed", "Error in creating patient.");
                    }
                }
            }
        } catch (err) {
            console.error("Unexpected error:", err);
            showError("Submission Failed", "An unexpected error occurred.");
        }
        finally {
            setIsSubmitting(false); // ✅ cleanup
        }
    };

    const addAddress = () => {
        const hasPermanent = form.addresses?.some(a => a.addressType === "Permanent");
        const hasPresent = form.addresses?.some(a => a.addressType === "Present");

        if (form.addresses?.length >= 2) {
            showError("Limit reached", "Only one Permanent and one Present address allowed.");
            return;
        }

        if (!hasPermanent) {
            setForm(prev => ({
                ...prev,
                addresses: [
                    ...(prev.addresses || []),
                    {
                        addressType: AddressType.Permanent,
                        houseNoOrFlatNo: null,
                        localityOrSector: null,
                        cityOrVillage: null,
                        pincode: null,
                        districtId: null,
                        stateId: null,
                        country: null,
                    },
                ],
            }));
        } else if (!hasPresent) {
            setForm(prev => ({
                ...prev,
                addresses: [
                    ...(prev.addresses || []),
                    {
                        addressType: AddressType.Present,
                        houseNoOrFlatNo: null,
                        localityOrSector: null,
                        cityOrVillage: null,
                        pincode: null,
                        districtId: null,
                        stateId: null,
                        country: null,
                    },
                ],
            }));
        } else {
            showError("Both addresses added", "You already have Permanent and Present addresses.");
        }
    };

    const onAddressTypeChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newType = e.target.value;
        const hasPermanent = form.addresses.some((a, i) => a.addressType === "Permanent" && i !== index);
        const hasPresent = form.addresses.some((a, i) => a.addressType === "Present" && i !== index);

        if (newType === "Permanent" && hasPermanent) {
            showError("Duplicate Permanent", "Only one Permanent address is allowed.");
            return;
        }
        if (newType === "Present" && hasPresent) {
            showError("Duplicate Present", "Only one Present address is allowed.");
            return;
        }

        setForm(prev => {
            const addresses = [...(prev.addresses || [])];
            addresses[index] = {
                ...addresses[index],
                addressType: newType === "Permanent" ? AddressType.Permanent : AddressType.Present,
            };
            return { ...prev, addresses };
        });
    };

    const onCheckboxChange = (index: number) => {
        const newVal = !presentSameAsPermanent;
        setPresentSameAsPermanent(newVal);

        setForm(prev => {
            const addresses = [...(prev.addresses || [])];
            const currentType = addresses[index]?.addressType;

            const sourceType =
                currentType === AddressType.Present
                    ? AddressType.Permanent
                    : AddressType.Present;

            if (newVal) {
                const source = addresses.find((a, i) => a.addressType === sourceType && i !== index);
                if (source) {
                    addresses[index] = { ...source, addressType: currentType };
                }
            } else {
                addresses[index] = {
                    addressType: currentType,
                    houseNoOrFlatNo: "",
                    localityOrSector: "",
                    cityOrVillage: "",
                    pincode: "",
                    districtId: "",
                    stateId: "",
                    country: "",
                };
            }

            return { ...prev, addresses };
        });
    };



    const isEditMode = Boolean(patientId);

    useEffect(() => {
        if (form.dateOfBirth) {
            const birthDate = new Date(form.dateOfBirth);
            const today = new Date();

            let years = today.getFullYear() - birthDate.getFullYear();
            let months = today.getMonth() - birthDate.getMonth();
            let days = today.getDate() - birthDate.getDate();

            if (days < 0) {
                // Go to previous month
                months--;
                const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                days += prevMonth.getDate();
            }

            if (months < 0) {
                months += 12;
                years--;
            }

            const formattedAge = `${years}Y ${months}M ${days}D`;

            if (form.age !== formattedAge) {
                setForm(prev => ({ ...prev, age: formattedAge }));
            }
        }
    }, [form.dateOfBirth]);


    useEffect(() => {
        if (!patientId && quickFormData?.firstName) {
            const storedFacilityName = localStorage.getItem("Facility");
            const facilityNameToIdMap: Record<string, number> = {
                "City Health Center": 1,
                "Green Valley Hospital": 2,
                "Civil Hospital Shillong": 3,
            };
            const facilityId = facilityNameToIdMap[storedFacilityName];
            if (!facilityId) {
                showError("Invalid Facility", `No numeric ID found for: ${storedFacilityName}`);
                return;
            }

            setForm({
                ...quickFormData,
                facilityId: facilityId.toString(),
            });
            clearQuickFormData();
        }
    }, [patientId, quickFormData, clearQuickFormData]);

    useEffect(() => {
        const fetchPatient = async () => {
            if (!patientId) return;

            try {
                const result = await getPatientById(patientId);
                if (Array.isArray(result) && result.length > 0) {
                    const fetched = result[0];
                    const normalizedOriginal: PatientRegistrationPayload = {
                        ...fetched,
                        facilityId: fetched.facilityId?.toString(),
                        insurance: {
                            ...fetched.insurance,
                            coverageAmount:
                                fetched.insurance?.coverageAmount !== undefined &&
                                    fetched.insurance.coverageAmount !== null &&
                                    String(fetched.insurance.coverageAmount) !== ""
                                    ? Number(fetched.insurance.coverageAmount)
                                    : undefined,
                        },
                        annualIncome: fetched.annualIncome || "",
                    };

                    setForm(normalizedOriginal);
                    setOriginalForm(normalizedOriginal);
                }
                else {
                    console.warn("No patient found for ID:", patientId);
                }
            } catch (error) {
                console.error("Error loading patient:", error);
            }
        };

        fetchPatient();
    }, [patientId]);

    useEffect(() => {
        if (form.addresses?.length === 2) {
            const permanent = form.addresses.find(a => a.addressType === "Permanent");
            const present = form.addresses.find(a => a.addressType === "Present");

            const fieldsToCompare = [
                "houseNoOrFlatNo",
                "localityOrSector",
                "cityOrVillage",
                "pincode",
                "districtId",
                "stateId",
                "country",
            ];

            const isSame = permanent && present && fieldsToCompare.every(
                key => permanent[key] === present[key]
            );

            setPresentSameAsPermanent(Boolean(isSame));
        } else {
            setPresentSameAsPermanent(false);
        }
    }, [form.addresses]);


    const handleInformationSharingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setForm(prev => ({
            ...prev,
            informationSharing: {
                ...prev.informationSharing,
                [name]: checked,
            },
        }));
    };

    return (
        <>
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
                <div className="container mx-auto max-w-8xl">
                    <div className="px-4 sm:px-0">
                        <h1 className="text-2xl font-bold text-gray-800 text-center">
                            {isEditMode ? "Edit Patient" : "Register Patient"}
                        </h1>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-1">
                        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                            {/* Patient Verification Section */}
                            {!isEditMode && (
                                <div className="p-2 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                                    <h2 className="text-xl font-bold text-gray-900 mb-2">ABHA Creation or Verification</h2>
                                    <PatientVerificationSection isEditMode={isEditMode} />
                                </div>
                            )}


                            {/* Basic Info */}
                            <div className="p-2 border-b border-gray-100">
                                <h2 className="text-xl font-bold text-gray-900 mb-2">Basic Information</h2>
                                <PatientBasicInfoSection
                                    form={form}
                                    formErrors={allErrors}
                                    isEditMode={isEditMode}
                                    onFieldChange={onFieldChange}
                                />
                            </div>

                            {/* Contact + Address */}
                            <div className="p-2 border-b border-gray-100">
                                <h2 className="text-xl font-bold text-gray-900 mb-2">Contact & Address</h2>
                                <PatientContactAddressSection
                                    form={form}
                                    setForm={setForm}
                                    formErrors={allErrors}
                                    onFieldChange={onFieldChange}
                                    onAddressTypeChange={onAddressTypeChange}
                                    addAddress={addAddress}
                                    onCheckboxChange={onCheckboxChange}
                                    presentSameAsPermanent={presentSameAsPermanent}
                                />

                            </div>

                            {/* Additional Info */}
                            <div className="p-2 border-b border-gray-100">
                                <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Information</h2>
                                <PatientAdditionalInfoSection
                                    form={form}
                                    setForm={setForm}
                                    formErrors={allErrors}
                                    isEditMode={isEditMode}
                                    onFieldChange={onFieldChange}
                                    handleInformationSharingChange={handleInformationSharingChange}
                                />
                            </div>

                            {/* Buttons */}
                            <div className="bg-gray-50 px-8 py-8 mx-auto">
                                <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                                    <button
                                        type="button"
                                        className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg shadow hover:bg-gray-100"
                                        onClick={() => navigate("/list")}
                                    >
                                        Cancel
                                    </button>

                                    <button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow hover:from-blue-700 hover:to-indigo-700"
                                    >
                                        {isSubmitting ? (
                                            <span className="flex items-center gap-2">
                                                <svg
                                                    className="animate-spin h-4 w-4 text-white"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <circle
                                                        className="opacity-25"
                                                        cx="12"
                                                        cy="12"
                                                        r="10"
                                                        stroke="currentColor"
                                                        strokeWidth="4"
                                                    ></circle>
                                                    <path
                                                        className="opacity-75"
                                                        fill="currentColor"
                                                        d="M4 12a8 8 0 018-8v4l3.5-3.5L12 0v4a8 8 0 00-8 8z"
                                                    ></path>
                                                </svg>
                                                {isEditMode ? "Saving..." : "Registering..."}
                                            </span>
                                        ) : (
                                            isEditMode ? "Save" : "Register"
                                        )}

                                    </button>
                                </div>
                            </div>

                        </div>
                    </form>

                </div>
            </div>
            {showTokenModal && createdPatient && (
                <TokenModal
                    isOpen={showTokenModal}
                    onClose={() => {
                        setShowTokenModal(false);
                        navigate("/list");
                    }}
                    patient={createdPatient}
                />
            )}


            <ConfirmDialog
                isOpen={showNoChangeModal}
                title="No Changes Detected"
                message="No changes were made. Do you want to return to the patient list?"
                onConfirm={() => {
                    setShowNoChangeModal(false);
                    navigate("/list");
                }}
                onCancel={() => {
                    setShowNoChangeModal(false);
                }}
            />

        </>
    );
};
