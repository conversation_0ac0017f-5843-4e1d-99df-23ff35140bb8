import axios from "axios";

const BASE_URL = "https://megha-dev.sirobilt.com";

// Waiting Room API Types
export interface BreakTime {
  startTime: string; // HH:MM
  endTime: string;   // HH:MM
  type: 'Lunch' | 'Tea' | 'Personal' | 'Meeting';
}

export interface Doctor {
  doctorId: string;
  fullName: string;
  isAvailable: boolean;
  nextAvailableTime: string; // HH:MM:SS format
  totalSlots: number;
  openSlots: number;
  experience: number;
  languages: string[];
}

export interface Department {
  departmentId: string;
  departmentName: string;
  totalDoctors: number;
  availableDoctors: number;
  doctors: Doctor[];
}

export interface DoctorAvailabilityResponse {
  date: string; // YYYY-MM-DD
  departments: Department[];
}

export interface DoctorStatusUpdate {
  doctorId: string;
  status: 'Available' | 'Busy' | 'Break' | 'Offline' | 'Emergency';
  currentPatientCount?: number;
  estimatedWaitTime?: number;
  nextAvailableTime?: string;
  lastStatusChange: string; // ISO datetime
}

export interface DoctorStatusResponse {
  success: boolean;
  timestamp: string;
  data: {
    lastUpdated: string;
    updates: DoctorStatusUpdate[];
  };
}

// Note: Department information is now retrieved from DoctorAvailabilityResponse.data.departments
// DepartmentInfo and DepartmentResponse interfaces are no longer needed

// Get Today's Doctor Availability
export const getTodaysDoctorAvailability = async (): Promise<DoctorAvailabilityResponse | null> => {
  try {
    console.log("🔄 Fetching today's doctor availability from:", `${BASE_URL}/api/waiting-room/doctors/today`);

    const response = await axios.get(`${BASE_URL}/api/waiting-room/doctors/today`);

    console.log("✅ Successfully fetched doctor availability:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("❌ Failed to fetch today's doctor availability:", error);
    console.error("Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: `${BASE_URL}/api/waiting-room/doctors/today`
    });

    return null;
  }
};

// Get Doctor Status Updates
export const getDoctorStatus = async (): Promise<DoctorStatusResponse> => {
  try {
    console.log("🔄 Fetching doctor status updates from:", `${BASE_URL}/api/waiting-room/doctors/status`);

    const response = await axios.get(`${BASE_URL}/api/waiting-room/doctors/status`);

    console.log("✅ Successfully fetched doctor status:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("❌ Failed to fetch doctor status:", error);
    console.error("Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: `${BASE_URL}/api/waiting-room/doctors/status`
    });

    return {
      success: false,
      timestamp: new Date().toISOString(),
      data: {
        lastUpdated: new Date().toISOString(),
        updates: []
      }
    };
  }
};

// Note: Department information is now retrieved from the doctors/today API response
// The getWaitingRoomDepartments function has been removed as departments are included
// in the DoctorAvailabilityResponse.data.departments array

// Utility function to format time
export const formatTime = (timeString: string): string => {
  if (!timeString) return '';
  
  // Handle both HH:MM and HH:MM:SS formats
  const parts = timeString.split(':');
  if (parts.length >= 2) {
    return `${parts[0]}:${parts[1]}`;
  }
  
  return timeString;
};

// Utility function to get status color
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Available':
      return 'text-green-600 bg-green-100';
    case 'Busy':
      return 'text-yellow-600 bg-yellow-100';
    case 'Break':
      return 'text-blue-600 bg-blue-100';
    case 'Offline':
      return 'text-gray-600 bg-gray-100';
    case 'Emergency':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// Utility function to calculate estimated wait time display
export const formatWaitTime = (minutes?: number): string => {
  if (!minutes || minutes <= 0) return 'No wait';
  
  if (minutes < 60) {
    return `${minutes} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};
