// Department Management Types

export interface Department {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string; // Short code like "CARD", "ORTHO", "PEDIA"
  description?: string;
  
  // Department Head
  headOfDepartment?: string; // Provider ID
  
  // Contact Information
  phoneNumber?: string;
  email?: string;
  location?: string; // Floor/Wing/Building
  
  // Operational Details
  operatingHours: DepartmentOperatingHours[];
  services: string[]; // List of services offered
  
  // Status
  isActive: boolean;
  isEmergencyDepartment: boolean;
  
  // Metadata
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface DepartmentOperatingHours {
  dayOfWeek: string; // "MONDAY", "TUESDAY", etc.
  isOperating: boolean;
  startTime: string; // "08:00:00"
  endTime: string; // "18:00:00"
  breakStartTime?: string;
  breakEndTime?: string;
  emergencyHours?: boolean; // 24/7 for emergency departments
}

// Provider-Department Mapping
export interface ProviderDepartmentMapping {
  mappingId: string;
  providerId: string;
  departmentId: string;
  facilityId: string;
  
  // Role in Department
  role: DepartmentRole;
  isPrimary: boolean; // Primary department for the provider
  
  // Effective Period
  effectiveFrom: string;
  effectiveTo?: string;
  
  // Status
  isActive: boolean;
  
  // Metadata
  createdAt?: string;
  updatedAt?: string;
  assignedBy?: string;
}

export enum DepartmentRole {
  Head = "HEAD",
  Senior = "SENIOR",
  Junior = "JUNIOR",
  Consultant = "CONSULTANT",
  Resident = "RESIDENT",
  Intern = "INTERN",
  Visiting = "VISITING"
}

// Department Statistics
export interface DepartmentStats {
  departmentId: string;
  departmentName: string;
  
  // Provider Statistics
  totalProviders: number;
  activeProviders: number;
  availableProviders: number;
  
  // Appointment Statistics
  appointmentsToday: number;
  appointmentsThisWeek: number;
  appointmentsThisMonth: number;
  
  // Queue Statistics
  currentQueueLength: number;
  averageWaitTime: number; // in minutes
  
  // Utilization
  utilizationRate: number; // percentage
  peakHours: string[];
  
  // Last Updated
  lastUpdated: string;
}

// Department with Provider Information
export interface DepartmentWithProviders {
  department: Department;
  providers: ProviderDepartmentInfo[];
  stats: DepartmentStats;
}

export interface ProviderDepartmentInfo {
  providerId: string;
  fullName: string;
  title: string;
  specialization: string;
  role: DepartmentRole;
  isPrimary: boolean;
  isActive: boolean;
  isAvailable: boolean;
  currentQueueLength?: number;
  nextAvailableSlot?: string;
}

// Department Filters
export interface DepartmentFilters {
  facilityId?: string;
  isActive?: boolean;
  isEmergencyDepartment?: boolean;
  hasProviders?: boolean;
  searchTerm?: string;
  page?: number;
  size?: number;
}

// Department Service Configuration
export interface DepartmentService {
  serviceId: string;
  departmentId: string;
  serviceName: string;
  serviceCode: string;
  description?: string;
  
  // Service Details
  averageServiceTime: number; // in minutes
  maxConcurrentPatients: number;
  requiresAppointment: boolean;
  allowsWalkIn: boolean;
  
  // Pricing
  basePrice?: number;
  currency?: string;
  
  // Status
  isActive: boolean;
  
  // Metadata
  createdAt?: string;
  updatedAt?: string;
}

// Queue by Department
export interface DepartmentQueue {
  departmentId: string;
  departmentName: string;
  departmentCode: string;
  
  // Queue Information
  totalInQueue: number;
  currentlyServing?: {
    providerId: string;
    providerName: string;
    patientName: string;
    queueNumber: number;
    serviceStartTime: string;
  };
  
  // Provider Queues
  providerQueues: ProviderQueueInfo[];
  
  // Department Statistics
  averageWaitTime: number;
  estimatedWaitTime: number;
  peakHours: string[];
  
  // Last Updated
  lastUpdated: string;
}

export interface ProviderQueueInfo {
  providerId: string;
  providerName: string;
  providerTitle: string;
  specialization: string;
  
  // Queue Details
  queueLength: number;
  currentPatient?: {
    patientName: string;
    queueNumber: number;
    serviceStartTime: string;
  };
  nextPatients: {
    patientName: string;
    queueNumber: number;
    estimatedTime: string;
  }[];
  
  // Provider Status
  isAvailable: boolean;
  status: string; // "AVAILABLE", "BUSY", "BREAK", "OFFLINE"
  
  // Wait Time
  averageServiceTime: number;
  estimatedWaitTime: number;
}

// Today's Available Doctors
export interface TodayAvailableDoctor {
  providerId: string;
  name: string;
  title: string;
  specialization: string;
  isAvailable: boolean;
  hasScheduleToday: boolean;
  scheduleTime: string | null;
  currentStatus: string; // "Available", "Busy", "Offline", "No Schedule"
  nextAvailableTime: string | null;
  patientsToday: number;
  currentQueueLength: number;
}

export interface DepartmentAvailableDoctors {
  departmentId: string;
  departmentName: string;
  departmentCode: string;
  totalDoctors: number;
  availableDoctors: number;
  doctors: TodayAvailableDoctor[];
}

// Export commonly used types
export type {
  Department as DepartmentType,
  ProviderDepartmentMapping as ProviderDepartmentMappingType,
  DepartmentStats as DepartmentStatsType,
  DepartmentWithProviders as DepartmentWithProvidersType,
  DepartmentQueue as DepartmentQueueType,
  TodayAvailableDoctor as TodayAvailableDoctorType,
  DepartmentAvailableDoctors as DepartmentAvailableDoctorsType
};

// Export enums - already exported above

// Default department roles for dropdowns
export const departmentRoleOptions = [
  { value: DepartmentRole.Head, label: "Head of Department" },
  { value: DepartmentRole.Senior, label: "Senior Doctor" },
  { value: DepartmentRole.Junior, label: "Junior Doctor" },
  { value: DepartmentRole.Consultant, label: "Consultant" },
  { value: DepartmentRole.Resident, label: "Resident" },
  { value: DepartmentRole.Intern, label: "Intern" },
  { value: DepartmentRole.Visiting, label: "Visiting Doctor" }
];

// Common departments for healthcare facilities
export const commonDepartments = [
  { code: "CARD", name: "Cardiology", description: "Heart and cardiovascular diseases" },
  { code: "ORTHO", name: "Orthopedics", description: "Bone and joint disorders" },
  { code: "PEDIA", name: "Pediatrics", description: "Children's healthcare" },
  { code: "GYNE", name: "Gynecology", description: "Women's reproductive health" },
  { code: "NEURO", name: "Neurology", description: "Nervous system disorders" },
  { code: "DERM", name: "Dermatology", description: "Skin and hair disorders" },
  { code: "ENT", name: "ENT", description: "Ear, Nose, and Throat" },
  { code: "OPTH", name: "Ophthalmology", description: "Eye care and vision" },
  { code: "PSYCH", name: "Psychiatry", description: "Mental health and behavioral disorders" },
  { code: "EMER", name: "Emergency", description: "Emergency and trauma care" },
  { code: "ICU", name: "Intensive Care", description: "Critical care unit" },
  { code: "RADIO", name: "Radiology", description: "Medical imaging and diagnostics" },
  { code: "LAB", name: "Laboratory", description: "Pathology and laboratory services" },
  { code: "PHARM", name: "Pharmacy", description: "Medication and pharmaceutical services" },
  { code: "PHYSIO", name: "Physiotherapy", description: "Physical therapy and rehabilitation" }
];
