import React, { useState, useEffect } from 'react';
import { User, Building2 } from 'lucide-react';
import { Select } from '../commonfields/Select';
import { Button } from '../commonfields/Button';
import DoctorInterface from './DoctorInterface';
import { getDoctors, type Doctor } from '../services/scheduleApis';
import { ServiceType } from '../types/queue';
import type { Provider } from '../types/provider';

const DoctorInterfaceWrapper: React.FC = () => {
  const [selectedProviderId, setSelectedProviderId] = useState<string>('');
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>('1'); // Default facility
  const [selectedServiceType, setSelectedServiceType] = useState<ServiceType>(ServiceType.Consultation);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [showInterface, setShowInterface] = useState(false);

  useEffect(() => {
    loadProviders();
  }, []);

  // Transform Doctor to Provider format
  const transformDoctorToProvider = (doctor: Doctor): Provider => {
    const nameParts = doctor.fullName.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      providerId: doctor.doctorId,
      facilityId: '',
      title: 'Dr.',
      firstName,
      lastName,
      email: '',
      phoneNumber: '',
      specialization: doctor.specialization as any,
      licenseNumber: doctor.registrationNumber,
      qualification: '',
      experience: doctor.yearsOfExperience,
      department: '',
      isActive: doctor.isActive,
      status: doctor.isActive ? 'Available' as any : 'Unavailable' as any,
      defaultSlotDuration: 30 as any,
      workingHours: [],
    };
  };

  const loadProviders = async () => {
    setLoading(true);
    try {
      const response = await getDoctors();
      if (response.success && response.data) {
        const transformedProviders = response.data
          .filter(doctor => doctor.isActive)
          .map(transformDoctorToProvider);
        setProviders(transformedProviders);
      } else {
        console.error('Failed to load doctors:', response.error);
      }
    } catch (error) {
      console.error('Failed to load providers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartSession = () => {
    if (selectedProviderId && selectedFacilityId) {
      setShowInterface(true);
    }
  };

  const handleEndSession = () => {
    setShowInterface(false);
  };

  if (showInterface) {
    return (
      <div>
        {/* Session Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="text-blue-600" size={20} />
                <span className="font-medium">
                  {providers.find(p => p.providerId === selectedProviderId)?.firstName} {' '}
                  {providers.find(p => p.providerId === selectedProviderId)?.lastName}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Building2 className="text-gray-600" size={16} />
                <span className="text-sm text-gray-600">Facility: {selectedFacilityId}</span>
              </div>
              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                {selectedServiceType}
              </div>
            </div>
            <Button
              onClick={handleEndSession}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              End Session
            </Button>
          </div>
        </div>

        {/* Doctor Interface */}
        <DoctorInterface
          providerId={selectedProviderId}
          facilityId={selectedFacilityId}
          serviceType={selectedServiceType}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-8">
        <div className="text-center mb-8">
          <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <User className="text-blue-600" size={32} />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Doctor Interface</h1>
          <p className="text-gray-600">Select your details to start your session</p>
        </div>

        <div className="space-y-6">
          {/* Provider Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Doctor
            </label>
            <Select
              value={selectedProviderId}
              onChange={(e) => setSelectedProviderId(e.target.value)}
              disabled={loading}
              className="w-full"
            >
              <option value="">
                {loading ? 'Loading doctors...' : 'Select a doctor'}
              </option>
              {providers.map((provider) => (
                <option key={provider.providerId} value={provider.providerId}>
                  {provider.title} {provider.firstName} {provider.lastName} - {provider.specialization || 'No specialization'}
                </option>
              ))}
            </Select>
          </div>

          {/* Facility Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Facility
            </label>
            <Select
              value={selectedFacilityId}
              onChange={(e) => setSelectedFacilityId(e.target.value)}
              className="w-full"
            >
              <option value="1">Main Hospital</option>
              <option value="2">Outpatient Clinic</option>
              <option value="3">Emergency Department</option>
            </Select>
          </div>

          {/* Service Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department/Service
            </label>
            <Select
              value={selectedServiceType}
              onChange={(e) => setSelectedServiceType(e.target.value as ServiceType)}
              className="w-full"
            >
              {Object.values(ServiceType).map((serviceType) => (
                <option key={serviceType} value={serviceType}>
                  {serviceType.charAt(0).toUpperCase() + serviceType.slice(1)}
                </option>
              ))}
            </Select>
          </div>

          {/* Start Session Button */}
          <Button
            onClick={handleStartSession}
            disabled={!selectedProviderId || !selectedFacilityId}
            className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Start Session
          </Button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Make sure to select the correct department for accurate queue management
          </p>
        </div>
      </div>
    </div>
  );
};

export default DoctorInterfaceWrapper;
