import React, { useState, useEffect } from "react";
import { handleChange, handleArrayChange } from "../../hooks/useFormHandlers";
import { defaultPatientRegistrationPayload } from "../../types/patient";
import type { PatientRegistrationPayload } from "../../types/patient";
import { FormField } from "../../../src/commonfields/FormField";
import { Input } from "../../../src/commonfields/Input";
import { Select } from "../../../src/commonfields/Select";
import { Calendar } from "../../../src/commonfields/Calendar";
import { Button } from "../../../src/commonfields/Button";
import { Link } from "react-router-dom";
import { allowOnlyNumbersWithLimit } from "../../inputhelpers/inputHelpers";
import {
  titleOptions,
  genderOptions,
  relationTypeOptions,
  identifierTypeOptions,
} from "../../types/patientenums";
import { createPatient } from "../../services/patientApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { usePatientFormStore } from "../../store/patientFormStore";
import { patientSchema } from "../../zod_validations/patient/patientSchema";
import { ZodError } from "zod";
import FormMessage from "../../commonfields/FormMessage";
import { allowOnlyLettersWithSpecialChars } from "../../inputhelpers/inputHelpers";
import { getFreshPatientForm } from "../../utils/patientPayloadResetUtil";

type Props = {
  onClose: () => void;
  onSuccess?: () => void;
};

const facilityNameToIdMap: Record<string, number> = {
  "City Health Center": 1,
  "Green Valley Hospital": 2,
  "Civil Hospital Shillong": 3,
};

export const RegisterPatientDrawer: React.FC<Props> = ({ onClose, onSuccess }) => {
  const [form, setForm] = useState<PatientRegistrationPayload>(getFreshPatientForm());
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const { setQuickFormData } = usePatientFormStore();

  const onBasicChange = handleChange(setForm);
  const onContactChange = handleArrayChange(setForm, "contacts", 0);
  const onEmergencyChange = handleArrayChange(setForm, "emergencyContacts", 0);

  const onBasicChangeWithIdentifierCheck = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name } = e.target;
    onBasicChange(e);

    if (name === "identifierNumber" && !form.identifierType) {
      setFormErrors((prev) => ({
        ...prev,
        identifierType: "Please select Identifier Type before entering Identifier Number",
      }));
    }

    if (name === "identifierType") {
      setFormErrors((prev) => {
        const { identifierType: _, ...rest } = prev;
        return rest;
      });
    }
  };

 useEffect(() => {
  // Reset the form and errors when the drawer opens
  setFormErrors({});
  const storedFacilityName = localStorage.getItem("Facility");
  const facilityId = facilityNameToIdMap[storedFacilityName ?? ""];

  if (!facilityId) {
    showError("Invalid Facility", `No numeric ID found for: ${storedFacilityName}`);
    setForm(defaultPatientRegistrationPayload); // Still reset even if facility is invalid
    return;
  }

  setForm({
    ...defaultPatientRegistrationPayload,
    facilityId: facilityId.toString(),
  });
}, []);


  useEffect(() => {
  if (form.dateOfBirth) {
    const birthDate = new Date(form.dateOfBirth);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();
    let days = today.getDate() - birthDate.getDate();

    if (days < 0) {
      // Go to previous month
      months--;
      const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      days += prevMonth.getDate();
    }

    if (months < 0) {
      months += 12;
      years--;
    }

    const formattedAge = `${years}Y ${months}M ${days}D`;

    if (form.age !== formattedAge) {
      setForm(prev => ({ ...prev, age: formattedAge }));
    }
  }
}, [form.dateOfBirth]);


  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setForm((prev) => ({
      ...prev,
      dateOfBirth: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      patientSchema.parse(form);
      setFormErrors({});

      const fullName = `${form.firstName} ${form.middleName || ""} ${form.lastName}`.trim();
      const { success, data, error } = await createPatient(form);

      if (success) {
        showSuccess("Quick Registration Successful", fullName);
        window.dispatchEvent(new Event("patient:registered"));
        setForm(defaultPatientRegistrationPayload);
  setFormErrors({});

        if (onSuccess) onSuccess();
        onClose();
      } else {
        // Normalize and extract error message
        let errorMessage = "";
        if (typeof error === "string") {
          errorMessage = error;
        } else if (typeof error === "object" && error !== null) {
          const err = error as any;
          errorMessage = err?.data?.message || err?.message || "";
        }

        console.error("Patient Create Error:", error);

        if (errorMessage.toLowerCase().includes("duplicate")) {
          showError("Patient Already Exists", "A similar patient already exists.");
        } else {
          showError("Creating Patient Failed", errorMessage || "Error in creating patient.");
        }
      }
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((e) => {
          const path = e.path.join(".");
          fieldErrors[path] = e.message;
        });
        setFormErrors(fieldErrors);
        showError("Form Error", "Please fix the highlighted fields.");
      } else {
        showError("Submission Failed", "An unexpected error occurred.");
      }
    }
  };

  return (
    <div className="fixed top-0 right-0 w-full sm:max-w-md h-full bg-white border-l border-gray-200 shadow-2xl z-50 flex flex-col">
      <div className="p-6 flex justify-between items-center border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-blue-50">
        <h2 className="text-lg font-semibold text-gray-800">Quick Register Patient</h2>
        <button onClick={onClose} className="text-xl font-bold text-gray-600 hover:text-red-500">&times;</button>
      </div>

      <form onSubmit={handleSubmit} autoComplete="off" className="flex-1 overflow-y-auto p-6 space-y-6 bg-white">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <FormField label="Title" required>
            <Select name="title" value={form.title || ""} onChange={onBasicChange}>
              <option value="">Select</option>
              {titleOptions.map((t) => <option key={t} value={t}>{t}</option>)}
            </Select>
            <FormMessage>{formErrors["title"]}</FormMessage>
          </FormField>

          <FormField label="First Name" required>
            <Input name="firstName"   autoComplete="off" value={form.firstName || ""} onChange={onBasicChange} />
            <FormMessage>{formErrors["firstName"]}</FormMessage>
          </FormField>

          <FormField label="Middle Name">
            <Input name="middleName"  autoComplete="off" value={form.middleName || ""} onChange={onBasicChange} />
          </FormField>

          <FormField label="Last Name" required>
            <Input name="lastName"  autoComplete="off" value={form.lastName || ""} onChange={onBasicChange} />
            <FormMessage>{formErrors["lastName"]}</FormMessage>
          </FormField>

          <FormField label="Date of Birth" required>
            <Calendar name="dateOfBirth" value={form.dateOfBirth || ""} onChange={handleDateChange} />
            <FormMessage>{formErrors["dateOfBirth"]}</FormMessage>
          </FormField>

          <FormField label="Gender" required>
            <Select name="gender" value={form.gender || ""} onChange={onBasicChange}>
              <option value="">Select</option>
              {genderOptions.map((g) => <option key={g} value={g}>{g}</option>)}
            </Select>
            <FormMessage>{formErrors["gender"]}</FormMessage>
          </FormField>

          <FormField label="Phone Number" required>
            <Input
              name="phoneNumber"
               autoComplete="off"
              value={form.contacts?.[0]?.phoneNumber || ""}
              onChange={onContactChange}
              onKeyDown={allowOnlyNumbersWithLimit(10)}
            />
            <FormMessage>{formErrors["contacts.0.phoneNumber"]}</FormMessage>
          </FormField>

          <FormField label="Emergency Contact Name">
            <Input
              name="contactName"
               autoComplete="off"
              value={form.emergencyContacts?.[0]?.contactName || ""}
              onChange={onEmergencyChange}
            />
            <FormMessage>{formErrors["emergencyContacts.0.contactName"]}</FormMessage>
          </FormField>

          <FormField label="Relationship">
            <Select
              name="relationship"
               autoComplete="off"
              value={form.emergencyContacts?.[0]?.relationship || ""}
              onChange={onEmergencyChange}
            >
              <option value="">Select</option>
              {relationTypeOptions.map((r) => <option key={r} value={r}>{r}</option>)}
            </Select>
            <FormMessage>{formErrors["emergencyContacts.0.relationship"]}</FormMessage>
          </FormField>

          <FormField label="Emergency Phone Number">
            <Input
             autoComplete="off"
              name="phoneNumber"
              value={form.emergencyContacts?.[0]?.phoneNumber || ""}
              onChange={onEmergencyChange}
              onKeyDown={allowOnlyNumbersWithLimit(10)}
            />
            <FormMessage>{formErrors["emergencyContacts.0.phoneNumber"]}</FormMessage>
          </FormField>


          <FormField label="Age (calculated)">
            <Input  autoComplete="off" name="age" value={form.age?.toString() || ""} disabled />
          </FormField>
        </div>

        <div className="pt-4 border-t border-gray-200 flex items-center justify-between">
          <Link
            to="/patients"
            className="text-sm text-blue-600 hover:underline"
            onClick={() => {
              setQuickFormData(form);
              onClose();
            }}
          >
            + Add more details
          </Link>
          <div className="flex gap-4">
            <Button type="submit" variant="primary">Register Patient</Button>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </div>
        </div>
      </form>
    </div>
  );
};
