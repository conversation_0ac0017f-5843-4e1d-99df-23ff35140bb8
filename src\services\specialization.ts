// import axios from "axios";

// const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// export type Specialization = {
//   specializationId: string;
//   name: string;
//   code: string;
//   isClinical: boolean;
//   description: string;
// };

// export const getAllSpecializations = async (): Promise<Specialization[]> => {
//   try {
//     const response = await axios.get(`${BASE_URL}/api/specializations`);
//     return response.data;
//   } catch (error: any) {
//     console.error("Error fetching specializations:", error.response?.data || error.message);
//     return [];
//   }
// };
import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export type Specialization = {
  specializationId: string;
  name: string;
  code: string;
  isClinical: boolean;
  description: string;
};

export type Department = {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string;
  description: string;
  phoneNumber: string;
  email: string;
  location: string;
  operatingHours: any[];  // keep as any[] or more specific type if you want
  isActive: boolean;
  isEmergencyDepartment: boolean;
};

export const getAllDepartments = async (): Promise<Department[]> => {
  try {
    const response = await axios.get(`${BASE_URL}/api/departments`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching departments:", error.response?.data || error.message);
    return [];
  }
};

export const createSpecialization = async (data: Omit<Specialization, 'specializationId'>): Promise<Specialization | null> => {
  try {
    const response = await axios.post(`${BASE_URL}/api/specializations`, data);
    return response.data;
  } catch (error: any) {
    console.error("Error creating specialization:", error.response?.data || error.message);
    return null;
  }
};

export const updateSpecialization = async (
  id: string,
  data: Partial<Omit<Specialization, 'specializationId'>>
): Promise<Specialization | null> => {
  try {
    const response = await axios.put(`${BASE_URL}/api/specializations/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error("Error updating specialization:", error.response?.data || error.message);
    return null;
  }
};

export const getSpecializationById = async (id: string): Promise<Specialization | null> => {
  try {
    const response = await axios.get(`${BASE_URL}/api/specializations/${id}`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching specialization by ID:", error.response?.data || error.message);
    return null;
  }
};

export const deleteSpecialization = async (id: string): Promise<boolean> => {
  try {
    await axios.delete(`${BASE_URL}/api/specializations/${id}`);
    return true;
  } catch (error: any) {
    console.error("Error deleting specialization:", error.response?.data || error.message);
    return false;
  }
};
