import React, { useState, useEffect } from "react";
import { sendOtp, verifyOtp } from "../../services/otpService";
import { Input } from "../../commonfields/Input";
import { showError, showSuccess } from "../../utils/toastUtils";
import { digitsIncludingZero, allowOnlyNumbersWithLimit } from "../../inputhelpers/inputHelpers";

type Props = {
  phoneNumber: string;
  onChange: (value: string) => void;
};

export const MobileSMSOtp: React.FC<Props> = ({ phoneNumber, onChange }) => {
  const [otp, setOtp] = useState("");
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [verified, setVerified] = useState<null | boolean>(null);
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (resendTimer > 0) {
      timer = setTimeout(() => setResendTimer((prev) => prev - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [resendTimer]);

  const handleSendOtp = async () => {
    if (phoneNumber.length !== 10) {
      showError("Invalid", "Enter a valid 10-digit number");
      return;
    }

    setLoading(true);
    const result = await sendOtp(phoneNumber);
    setLoading(false);

    if (result.success) {
      showSuccess("OTP Sent", result.message);
      setIsOtpSent(true);
      setVerified(null);
      setResendTimer(30); // 30-second resend timer
    } else {
      showError("Failed", result.message);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp || otp.length !== 6) {
      showError("Invalid", "Enter a 6-digit OTP");
      return;
    }

    setLoading(true);
    const result = await verifyOtp(phoneNumber, otp);
    setLoading(false);

    if (result.success) {
      showSuccess("Verified", result.message);
      setVerified(true);
      setResendTimer(0);
    } else {
      showError("Failed", result.message);
      setVerified(false);
    }
  };

  return (
    <div className="space-y-2">
      {/* Phone Input + Send OTP */}
      <div className="grid grid-cols-3 gap-2 items-end">
        <Input
          name="phoneNumber"
          placeholder="Enter 10-digit number"
          value={phoneNumber}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={allowOnlyNumbersWithLimit(10)}
          className="col-span-2"
        />
        <button
          type="button"
          onClick={handleSendOtp}
          disabled={
            loading || resendTimer > 0 || verified === true || phoneNumber.length !== 10
          }
          className={`w-full h-[42px] px-3 py-2 text-sm rounded transition 
            ${verified
              ? "bg-green-100 text-green-700 cursor-default"
              : "bg-blue-600 text-white hover:bg-blue-700"} 
            disabled:opacity-60`}
        >
          {loading ? (
            "Sending..."
          ) : verified ? (
            "✅ Verified"
          ) : resendTimer > 0 ? (
            <span className="text-xs">Resend in {resendTimer}s</span>
          ) : (
            "Send OTP"
          )}
        </button>
      </div>

      {/* OTP Input + Verify Button */}
      {isOtpSent && !verified && (
        <div className="grid grid-cols-3 gap-2 items-end">
          <Input
            name="otp"
            placeholder="Enter 6-digit OTP"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            onKeyDown={digitsIncludingZero(6)}
            className="col-span-2"
          />
          <button
            type="button"
            onClick={handleVerifyOtp}
            disabled={loading}
            className="w-full h-[42px] bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 disabled:opacity-60"
          >
            {loading ? "Verifying..." : "Verify"}
          </button>
        </div>
      )}

      {/* ✅ Success Message ONLY after verified */}
      {verified && (
        <div className="text-sm font-medium text-green-600 flex items-center space-x-1">
          <span className="text-lg">✅</span>
          <span>Phone Verified</span>
        </div>
      )}
    </div>
  );
};
