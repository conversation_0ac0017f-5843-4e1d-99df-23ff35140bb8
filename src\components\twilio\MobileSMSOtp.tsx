import React, { useState } from "react";
import { sendOtp, verifyOtp } from "../../services/otpService";
import { Input } from "../../commonfields/Input";
import { showError, showSuccess } from "../../utils/toastUtils";
import { allowOnlyNumbersWithLimit } from "../../inputhelpers/inputHelpers";

type Props = {
    phoneNumber: string; // Expects just 10-digit number, not with +91
    onChange: (value: string) => void;
};

export const MobileSMSOtp: React.FC<Props> = ({ phoneNumber, onChange }) => {
    const [otp, setOtp] = useState("");
    const [isOtpSent, setIsOtpSent] = useState(false);
    const [verified, setVerified] = useState<null | boolean>(null);
    const [loading, setLoading] = useState(false);

    const handleSendOtp = async () => {
        if (phoneNumber.length !== 10) {
            showError("Invalid", "Enter a valid 10-digit number");
            return;
        }

        setLoading(true);
        const result = await sendOtp(phoneNumber);
        setLoading(false);

        if (result.success) {
            showSuccess("OTP Sent", result.message);
            setIsOtpSent(true);
            setVerified(null);
        } else {
            showError("Failed", result.message);
        }
    };

    const handleVerifyOtp = async () => {
        if (!otp || otp.length < 4) {
            showError("Invalid", "Enter valid OTP");
            return;
        }

        setLoading(true);
        const result = await verifyOtp(phoneNumber, otp);
        setLoading(false);

        if (result.success) {
            showSuccess("Verified", result.message);
            setVerified(true);
        } else {
            showError("Failed", result.message);
            setVerified(false);
        }
    };

    return (
        <div className="space-y-2">
            <div className="grid grid-cols-1 gap-2 items-end">
                <Input
                    name="phoneNumber"
                    placeholder="Enter 10-digit number"
                    value={phoneNumber}
                    onChange={(e) => onChange(e.target.value)}
                    className="col-span-2"
                    onKeyDown={allowOnlyNumbersWithLimit(10)} 
                />
                {/* <button
                    type="button"
                    onClick={handleSendOtp}
                    disabled={loading}
                    className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700"
                >
                    {loading ? "Sending..." : "Send OTP"}
                </button> */}
            </div>

            {isOtpSent && (
                <div className="grid grid-cols-3 gap-2 items-end">
                    <Input
                        name="otp"
                        placeholder="Enter OTP"
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        className="col-span-2"
                    />
                    <button
                        type="button"
                        onClick={handleVerifyOtp}
                        disabled={loading}
                        className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700"
                    >
                        {loading ? "Verifying..." : "Verify"}
                    </button>
                </div>
            )}

            {verified !== null && (
                <div className={`text-sm font-medium ${verified ? "text-green-600" : "text-red-600"}`}>
                    {verified ? "✅ Phone Verified" : "❌ Verification Failed"}
                </div>
            )}
        </div>
    );
};

// ====================================================================================
// same input field

// import React, { useState } from "react";
// import { sendOtp, verifyOtp } from "../../services/otpService";
// import { Input } from "../../commonfields/Input";
// import { showError, showSuccess } from "../../utils/toastUtils";

// type Props = {
//   phoneNumber: string; // Expects just 10-digit number, not with +91
//   onChange: (value: string) => void;
// };

// export const MobileSMSOtp: React.FC<Props> = ({ phoneNumber, onChange }) => {
//   const [otp, setOtp] = useState("");
//   const [isOtpSent, setIsOtpSent] = useState(false);
//   const [verified, setVerified] = useState<null | boolean>(null);
//   const [loading, setLoading] = useState(false);

//   const handleSendOtp = async () => {
//     if (phoneNumber.length !== 10) {
//       showError("Invalid", "Enter a valid 10-digit number");
//       return;
//     }

//     setLoading(true);
//     const result = await sendOtp(phoneNumber);
//     setLoading(false);

//     if (result.success) {
//       showSuccess("OTP Sent", result.message);
//       setIsOtpSent(true);
//       setVerified(null);
//     } else {
//       showError("Failed", result.message);
//     }
//   };

//   const handleVerifyOtp = async () => {
//     if (!otp || otp.length < 4) {
//       showError("Invalid", "Enter valid OTP");
//       return;
//     }

//     setLoading(true);
//     const result = await verifyOtp(phoneNumber, otp);
//     setLoading(false);

//     if (result.success) {
//       showSuccess("Verified", result.message);
//       setVerified(true);
//     } else {
//       showError("Failed", result.message);
//       setVerified(false);
//     }
//   };

//   return (
//     <div className="space-y-2">
//       <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 items-end">
//         {isOtpSent ? (
//           <>
//             <Input
//               name="otp"
//               placeholder="Enter OTP"
//               value={otp}
//               onChange={(e) => setOtp(e.target.value)}
//               className="col-span-1 sm:col-span-2"
//             />
//             <button
//               type="button"
//               onClick={handleVerifyOtp}
//               disabled={loading}
//               className="col-span-1 bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700"
//             >
//               {loading ? "Verifying..." : "Verify"}
//             </button>
//           </>
//         ) : (
//           <>
//             <Input
//               name="phoneNumber"
//               placeholder="Enter 10-digit number"
//               value={phoneNumber}
//               onChange={(e) => onChange(e.target.value)}
//               className="col-span-1 sm:col-span-2"
//             />
//             <button
//               type="button"
//               onClick={handleSendOtp}
//               disabled={loading}
//               className="col-span-1 bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700"
//             >
//               {loading ? "Sending..." : "Send OTP"}
//             </button>
//           </>
//         )}
//       </div>
//       {verified !== null && (
//         <div className={`text-sm font-medium ${verified ? "text-green-600" : "text-red-600"} col-span-1 sm:col-span-3`}>
//           {verified ? "✅ Phone Verified" : "❌ Verification Failed"}
//         </div>
//       )}
//     </div>
//   );
// };
