import React, { useState, useEffect } from 'react';
import { Play, Calendar, Clock, Users, AlertCircle, CheckCircle, Download } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Select } from '../../commonfields/Select';
import { Calendar as CalendarInput } from '../../commonfields/Calendar';
import { FormField } from '../../commonfields/FormField';
import FormMessage from '../../commonfields/FormMessage';
import { showSuccess, showError } from '../../utils/toastUtils';
import { getScheduleStats, getDoctors, generateConsultantSlots, type Doctor } from '../../services/scheduleApis';
import type { ScheduleStats } from '../../types/schedule';

interface SlotGenerationToolProps {
  facilityId?: string;
}

export const SlotGenerationTool: React.FC<SlotGenerationToolProps> = ({ facilityId }) => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [stats, setStats] = useState<ScheduleStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [generationResult, setGenerationResult] = useState<{ slotsGenerated: number; doctorId: string; dateRange: { from: string; to: string } } | null>(null);
  const [formData, setFormData] = useState<{
    from: string;
    to: string;
    doctorId: string;
  }>({
    from: new Date().toISOString().split('T')[0],
    to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    doctorId: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Load doctors/consultants
      const doctorsResponse = await getDoctors();
      if (doctorsResponse.success) {
        setDoctors(doctorsResponse.data || []);
      } else {
        showError(doctorsResponse.error || 'Failed to load doctors');
      }

      // Load schedule statistics
      const statsData = await getScheduleStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load initial data:', error);
      showError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.doctorId) {
      errors.doctorId = 'Doctor selection is required';
    }

    if (!formData.from) {
      errors.from = 'From date is required';
    }

    if (!formData.to) {
      errors.to = 'To date is required';
    }

    if (formData.from && formData.to && formData.from >= formData.to) {
      errors.to = 'To date must be after from date';
    }

    // Check if date range is not too large (e.g., max 90 days)
    if (formData.from && formData.to) {
      const fromDate = new Date(formData.from);
      const toDate = new Date(formData.to);
      const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff > 90) {
        errors.to = 'Date range cannot exceed 90 days';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleGenerateSlots = async () => {
    if (!validateForm()) return;

    setGenerating(true);
    setGenerationResult(null);

    try {
      // Find the selected doctor and get the correct consultantId
      const selectedDoctor = doctors.find(d => d.doctorId === formData.doctorId);
      if (!selectedDoctor) {
        showError('Selected doctor not found');
        return;
      }

      const consultantId = selectedDoctor.consultantId || selectedDoctor.doctorId;
      console.log('Generating slots for consultantId:', consultantId, 'from doctorId:', formData.doctorId);

      const result = await generateConsultantSlots(consultantId, {
        from: formData.from,
        to: formData.to
      });

      if (result.success) {
        setGenerationResult({
          slotsGenerated: result.data?.slotsGenerated || 0,
          doctorId: formData.doctorId,
          dateRange: { from: formData.from, to: formData.to }
        });
        showSuccess(`Successfully generated slots`);
        // Reload stats to reflect changes
        const updatedStats = await getScheduleStats();
        setStats(updatedStats);
      } else {
        showError(result.error || 'Failed to generate slots');
      }
    } catch (error) {
      console.error('Failed to generate slots:', error);
      showError('Failed to generate slots');
    } finally {
      setGenerating(false);
    }
  };

  const getDoctorName = (doctorId: string) => {
    const doctor = doctors.find(d => d.doctorId === doctorId);
    return doctor ? doctor.fullName : doctorId;
  };

  const formatDateRange = (from: string, to: string) => {
    const fromDate = new Date(from);
    const toDate = new Date(to);
    const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    return `${daysDiff} days (${from} to ${to})`;
  };

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = "blue" 
  }: { 
    title: string; 
    value: string | number; 
    icon: React.ReactNode; 
    color?: string;
  }) => (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full bg-${color}-100 text-${color}-600`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Slot Generation Tool</h1>
        <p className="text-gray-600">Generate appointment slots based on doctor schedules</p>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <StatCard
            title="Total Schedules"
            value={stats.totalSchedules}
            icon={<Calendar size={20} />}
            color="blue"
          />
          <StatCard
            title="Active Schedules"
            value={stats.activeSchedules}
            icon={<CheckCircle size={20} />}
            color="green"
          />
          <StatCard
            title="Available Slots"
            value={stats.availableSlots}
            icon={<Clock size={20} />}
            color="yellow"
          />
          <StatCard
            title="Booked Slots"
            value={stats.bookedSlots}
            icon={<Users size={20} />}
            color="purple"
          />
        </div>
      )}

      {/* Slot Generation Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-6">Generate Slots</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* From Date */}
          <FormField label="From Date" required>
            <CalendarInput
              value={formData.from}
              onChange={(e) => setFormData(prev => ({ ...prev, from: e.target.value }))}
              min={new Date().toISOString().split('T')[0]}
              required
            />
            {formErrors.from && <FormMessage>{formErrors.from}</FormMessage>}
          </FormField>

          {/* To Date */}
          <FormField label="To Date" required>
            <CalendarInput
              value={formData.to}
              onChange={(e) => setFormData(prev => ({ ...prev, to: e.target.value }))}
              min={formData.from || new Date().toISOString().split('T')[0]}
              required
            />
            {formErrors.to && <FormMessage>{formErrors.to}</FormMessage>}
          </FormField>

          {/* Doctor Selection */}
          <FormField label="Doctor Selection" required>
            <Select
              value={formData.doctorId}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                doctorId: e.target.value
              }))}
              required
            >
              <option value="">Select a Doctor</option>
              {doctors.map((doctor) => (
                <option key={doctor.doctorId} value={doctor.doctorId}>
                  {doctor.fullName} - {doctor.specialization?.name || 'No specialization'}
                </option>
              ))}
            </Select>
            {formErrors.doctorId && <FormMessage>{formErrors.doctorId}</FormMessage>}
          </FormField>
        </div>

        {/* Generation Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-blue-800 font-medium mb-2">Generation Summary</h3>
          <div className="text-blue-700 text-sm space-y-1">
            <p>• Date Range: {formatDateRange(formData.from, formData.to)}</p>
            <p>• Target: {formData.doctorId ? getDoctorName(formData.doctorId) : 'No doctor selected'}</p>
            <p>• Estimated Slots: {formData.doctorId ? '~150' : '0'} (based on existing schedules)</p>
          </div>
        </div>

        {/* Generate Button */}
        <div className="flex justify-center">
          <Button
            onClick={handleGenerateSlots}
            disabled={generating || loading}
            className="flex items-center space-x-2 px-8 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
          >
            <Play size={20} />
            <span>{generating ? 'Generating Slots...' : 'Generate Slots'}</span>
          </Button>
        </div>
      </div>

      {/* Generation Progress */}
      {generating && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <div>
              <h3 className="text-lg font-medium text-gray-800">Generating Slots...</h3>
              <p className="text-gray-600">Please wait while we create appointment slots based on doctor schedules.</p>
            </div>
          </div>
        </div>
      )}

      {/* Generation Results */}
      {generationResult && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-800">Generation Results</h3>
          </div>

          <div className="space-y-4">
            {/* Success Summary */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-green-800 font-medium mb-2">✅ Slots Generated Successfully</h4>
              <div className="text-green-700 text-sm space-y-1">
                <p>• Total Slots Generated: <strong>{generationResult.slotsGenerated}</strong></p>
                <p>• Date Range: <strong>{generationResult.dateRange.from} to {generationResult.dateRange.to}</strong></p>
                <p>• Doctor: <strong>{getDoctorName(generationResult.doctorId)}</strong></p>
              </div>
            </div>

            {/* Doctor Details */}
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Doctor Details:</h4>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Users size={16} />
                <span>{getDoctorName(generationResult.doctorId)}</span>
              </div>
            </div>
            </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 mt-6">
            <Button
              onClick={() => setGenerationResult(null)}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Close
            </Button>
            <Button
              onClick={() => {
                // Export functionality can be implemented here
                showSuccess('Export functionality coming soon');
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Download size={16} />
              <span>Export Report</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
