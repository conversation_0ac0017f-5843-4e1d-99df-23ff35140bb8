// hooks/useFormValidation.ts
import { useState, useCallback } from "react";
import { ZodSchema, ZodError } from "zod";

type ValidationErrors = Record<string, string>;

export const useFormValidation = <T>(schema: ZodSchema<T>) => {
  const [errors, setErrors] = useState<ValidationErrors>({});

  const validateField = useCallback((path: string, value: any, formData: T) => {
  try {
    schema.parse(formData); // attempt full parse to catch cross-field rules
    setErrors(prev => {
      const updated = { ...prev };
      delete updated[path];
      return updated;
    });
    return true;
  } catch (error) {
    if (error instanceof ZodError) {
      const fieldErrors: ValidationErrors = {};
      let foundErrorForPath = false;

      for (const issue of error.errors) {
        const joined = issue.path.join(".");
        fieldErrors[joined] = issue.message;

        // Only show error for the current path
        if (joined === path ) {
          foundErrorForPath = true;
        }
      }

      setErrors(prev => {
        const updated = { ...prev };
        if (foundErrorForPath) {
          updated[path] = fieldErrors[path];
        } else {
          delete updated[path]; // field is now valid
        }
        return updated;
      });

      return !foundErrorForPath;
    }
  }
}, [schema]);


  const validateForm = useCallback((formData: T): ValidationErrors => {
    try {
      schema.parse(formData);
      setErrors({});
      return {};
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: ValidationErrors = {};
        for (const issue of error.errors) {
          fieldErrors[issue.path.join(".")] = issue.message;
        }
        setErrors(fieldErrors);
        return fieldErrors;
      }
      return {};
    }
  }, [schema]);

  const clearFieldError = useCallback((fieldPath: string) => {
    setErrors(prev => {
      const updated = { ...prev };
      delete updated[fieldPath];
      return updated;
    });
  }, []);

  const clearAllErrors = useCallback(() => setErrors({}), []);

  return {
    errors,
    validateField,
    validateForm,
    clearFieldError,
    clearAllErrors,
    setErrors,
  };
};
