// API Configuration for MeghaSan<PERSON>vini Integration

export const API_CONFIG = {
  // Backend API base URL - always use direct backend URL
  BASE_URL: 'https://megha-dev.sirobilt.com/api',

  // Feature flags
  USE_MOCK_DATA: false, // Use real backend APIs
  AUTO_FALLBACK_TO_MOCK: false, // Do not fallback to mock data
  
  // API endpoints
  ENDPOINTS: {
    // Department Management
    DEPARTMENTS: '/departments',
    DEPARTMENT_BY_ID: (id: string) => `/departments/${id}`,
    DEPARTMENT_STATS: (id: string) => `/departments/${id}/stats`,
    
    // Provider-Department Mapping
    PROVIDER_DEPARTMENT_MAPPINGS: '/provider-department-mappings',
    PROVIDER_DEPARTMENT_MAPPING_BY_ID: (id: string) => `/provider-department-mappings/${id}`,
    
    // Queue Management
    DEPARTMENT_QUEUES: '/departments/queues',
    AVAILABLE_DOCTORS: '/departments/available-doctors',
    
    // Provider Management
    PROVIDERS: '/providers',
    PROVIDER_BY_ID: (id: string) => `/providers/${id}`,
    
    // Appointment Management
    APPOINTMENTS: '/appointments',
    APPOINTMENT_BY_ID: (id: string) => `/appointments/${id}`,
    APPOINTMENTS_QUERY: '/appointments/query',
    APPOINTMENT_CANCEL: (id: string) => `/appointments/${id}/cancel`,
    
    // Facility Management
    FACILITIES: '/facilities',
    FACILITY_BY_ID: (id: string) => `/facilities/${id}`,
  },
  
  // Request configuration
  REQUEST_CONFIG: {
    timeout: 30000, // 30 seconds
    retries: 3,
    retryDelay: 1000, // 1 second
  },
  
  // Authentication
  AUTH: {
    TOKEN_STORAGE_KEY: 'authToken',
    REFRESH_TOKEN_KEY: 'refreshToken',
    TOKEN_HEADER: 'Authorization',
    TOKEN_PREFIX: 'Bearer ',
  },
  
  // Error handling
  ERROR_CODES: {
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    VALIDATION_ERROR: 422,
    SERVER_ERROR: 500,
  },
  
  // Pagination defaults
  PAGINATION: {
    DEFAULT_PAGE: 0,
    DEFAULT_SIZE: 20,
    MAX_SIZE: 100,
  },
};

// Helper function to get full API URL
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Helper function to get auth headers
export const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem(API_CONFIG.AUTH.TOKEN_STORAGE_KEY) || 
                sessionStorage.getItem(API_CONFIG.AUTH.TOKEN_STORAGE_KEY);
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers[API_CONFIG.AUTH.TOKEN_HEADER] = `${API_CONFIG.AUTH.TOKEN_PREFIX}${token}`;
  }
  
  return headers;
};

// Helper function to handle API responses
export const handleApiResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ 
      message: `HTTP error! status: ${response.status}` 
    }));
    
    throw new Error(errorData.message || errorData.error || `HTTP error! status: ${response.status}`);
  }
  
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }
  
  return response.text();
};

// Helper function for API requests with retry logic
export const apiRequest = async (
  url: string, 
  options: RequestInit = {}, 
  retries: number = API_CONFIG.REQUEST_CONFIG.retries
): Promise<any> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.REQUEST_CONFIG.timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...getAuthHeaders(),
        ...options.headers,
      },
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return await handleApiResponse(response);
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (retries > 0 && error instanceof Error && !error.name.includes('AbortError')) {
      console.warn(`API request failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, API_CONFIG.REQUEST_CONFIG.retryDelay));
      return apiRequest(url, options, retries - 1);
    }
    
    throw error;
  }
};

// Environment-specific configurations
export const getEnvironmentConfig = () => {
  const isProd = import.meta.env.PROD;

  if (isProd) {
    return {
      ...API_CONFIG,
      BASE_URL: 'https://api.meghasanjeevini.com/api',
      USE_MOCK_DATA: false,
    };
  } else {
    // Development and staging - always use direct backend URL
    return {
      ...API_CONFIG,
      BASE_URL: 'https://megha-dev.sirobilt.com/api',
      USE_MOCK_DATA: false,
    };
  }
};

// Export the environment-specific config as default
export default getEnvironmentConfig();
