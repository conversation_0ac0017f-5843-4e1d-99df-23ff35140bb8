import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// DTO types
export interface ConsultantSlotConfigDto {
  consultantId: string;
  daysOfWeek: string[]; // Use DayOfWeek Enum string values: "MONDAY", "TUESDAY", etc.
  startTime: string;    // Format: "HH:mm"
  endTime: string;      // Format: "HH:mm"
  slotDuration: number;
  effectiveFrom: string; // Format: "yyyy-MM-dd"
  effectiveTo?: string;
}

export interface ConsultantSlotConfigResponseDto extends ConsultantSlotConfigDto {
  scheduleId: number;
}

// ==============================
// API FUNCTIONS
// ==============================

// 1. GET all configs (optionally by consultantId)
export const getConsultantSlotConfigs = async (
  consultantId?: string
): Promise<ConsultantSlotConfigResponseDto[]> => {
  try {
    const params = consultantId ? { consultantId } : {};
    const res = await axios.get(`${BASE_URL}/api/consultants/${consultantId}/slot-configs`, {
      params,
    });
    return res.data;
  } catch (err: any) {
    console.error("Failed to fetch consultant slot configs:", err.response?.data || err.message);
    return [];
  }
};

// 2. CREATE slot config for a consultant
export const createConsultantSlotConfig = async (
  consultantId: string,
  payload: ConsultantSlotConfigDto
): Promise<ConsultantSlotConfigDto | null> => {
  try {
    const res = await axios.post(
      `${BASE_URL}/api/consultants/${consultantId}/slot-configs`,
      payload
    );
    return res.data;
  } catch (err: any) {
    console.error("Failed to create consultant slot config:", err.response?.data || err.message);
    return null;
  }
};

// 3. UPDATE slot config for a consultant
export const updateConsultantSlotConfig = async (
  consultantId: string,
  payload: ConsultantSlotConfigDto
): Promise<ConsultantSlotConfigDto | null> => {
  try {
    const res = await axios.put(
      `${BASE_URL}/api/consultants/${consultantId}/slot-configs`,
      payload
    );
    return res.data;
  } catch (err: any) {
    console.error("Failed to update consultant slot config:", err.response?.data || err.message);
    return null;
  }
};

// 4. DELETE single config by configId
export const deleteSlotConfigById = async (configId: number): Promise<boolean> => {
  try {
    await axios.delete(`${BASE_URL}/api/slot-configs/${configId}`);
    return true;
  } catch (err: any) {
    console.error("Failed to delete slot config:", err.response?.data || err.message);
    return false;
  }
};

// 5. DELETE bulk configs by consultantId
export const deleteConfigsByConsultant = async (
  consultantId: string
): Promise<boolean> => {
  try {
    await axios.delete(`${BASE_URL}/api/slot-configs/consultant/${consultantId}`);
    return true;
  } catch (err: any) {
    console.error("Failed to delete slot configs by consultantId:", err.response?.data || err.message);
    return false;
  }
};
