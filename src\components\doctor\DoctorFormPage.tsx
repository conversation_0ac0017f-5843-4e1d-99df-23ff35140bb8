import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getDoctorById, createDoctor, updateDoctor } from "../../services/doctorApis";
import { ReactSelectInput } from "../../commonfields/ReactSelectInput";
import { z } from "zod"; // Ensure Zod is installed

// Import ALL validation helpers, both for onKeyDown and Zod refine
import {
  allowOnlyLettersWithSpecialChars, // Keep for onKeyDown
  allowOnlyNumbersWithLimit,       // Keep for onKeyDown
  allowOnlyLetters,          // Keep for onKeyDown
  allowAlphaNumericWithPolicyChars, // Keep for onKeyDown
  allowEmailCharactersOnly,  // Keep for onKeyDown
  digitsIncludingZero,       // Keep for onKeyDown
  allowOnlyCoverageAmount,   // Keep for onKeyDown (if used)

  // NEW for Zod validation
  validateMobileNumber,
  validatePostalCode,
  validateLettersWithSpecialChars,
  validateLetters,
  validateAlphaNumericWithPolicyChars,
  validateEmailCharactersOnly,
  // validateCoverageAmountString, // Add if you have a field for it
} from "../../inputhelpers/inputHelpers"; // Adjust path as needed

// languages.ts (or wherever you store constants)
export const languageOptions = [
  // General Indian Languages
  "Hindi",
  "English",
  "Bengali",
  "Telugu",
  "Khasi",
  "Garo",
  "Pnar",
  "Marathi",
  "Tamil",
  "Urdu",
  "Gujarati",
  "Kannada",
  "Odia",
  "Malayalam",
  "Punjabi",
  "Assamese",
  "Maithili",
  "Santali",
  "Konkani",
  "Manipuri",
  "Kashmiri",
  "Dogri",
  "Sindhi",
  "Bodo",
  "Nepali",
  "Sanskrit",
];

const qualificationMap = {
  "Administrator": [
    "MBA (Hospital Administration)",
    "MHA (Health Administration)",
    "Diploma in Hospital Management", "Others"
  ],
  "Doctor": [
    "MBBS",
    "MD (General Medicine)",
    "MS (General Surgery)",
    "DNB (Diplomate of National Board)",
    "DM (Cardiology)",
    "DM (Neurology)",
    "MCh (Cardiothoracic Surgery)",
    "DO (Doctor of Osteopathy)",
    "BAMS (Ayurveda)",
    "BHMS (Homeopathy)",
    "BUMS (Unani)",
    "MD (Ayurveda)",
    "MD (Homeopathy)",
    "PhD in Medicine",
    "Fellowship in Oncology",
    "Fellowship in Gastroenterology"
  ],
  "FrontOffice": [
    "Diploma in Hospital Management",
    "Certificate in Medical Receptionist Training",
    "BHM (Bachelor in Hospital Management)", "Others"
  ],
  "Report User": [
    "B.Sc (Medical Lab Technology)",
    "DMLT (Diploma in Medical Lab Technology)",
    "B.Sc (Radiology)",
    "Diploma in Radiology"
  ],
  "Therapist": [
    "BPT (Bachelor of Physiotherapy)",
    "MPT (Master of Physiotherapy)",
    "BOT (Bachelor of Occupational Therapy)",
    "MOT (Master of Occupational Therapy)",
    "BASLP (Audiology & Speech Language Pathology)",
    "Diploma in Occupational Therapy"
  ],
  "Patient": [],
  "Pharmacist": [
    "D.Pharm (Diploma in Pharmacy)",
    "B.Pharm (Bachelor of Pharmacy)",
    "M.Pharm (Master of Pharmacy)"
  ],
  "Clinic Manager": [
    "MBA (Healthcare Management)",
    "BHM (Bachelor of Hospital Management)",
    "Diploma in Clinic Administration"
  ],
  "Guest": [],
  "Counsellor": [
    "MA (Psychology)",
    "MSW (Master of Social Work - Medical & Psychiatry)",
    "Diploma in Mental Health Counselling",
    "Others"
  ],
  "Moderator": [
    "B.Sc (Health Information Management)",
    "Certificate in Healthcare IT",
    "Diploma in Medical Records Management"
  ]
};

const ROLE_TYPES = [
  "Administrator", "Doctor", "FrontOffice", "Report User", "Therapist", "Patient",
  "Pharmacist", "Clinic Manager", "Guest", "Counsellor", "Moderator"
];

const SPECIALIZATIONS = [
  "Internal Medicine", "Diabetology", "Infectious Diseases", "Geriatrics",
  "Laparoscopic Surgery", "Breast Surgery", "Trauma Surgery",
  "Joint Replacement", "Spine Surgery", "Sports Medicine",
  "Pediatric Neurology", "Pediatric Cardiology", "Neonatology",
  "Maternal-Fetal Medicine", "Infertility", "Gynecologic Oncology",
  "Interventional Cardiology", "Electrophysiology", "Non-Invasive Cardiology",
  "Stroke", "Epilepsy", "Neurophysiology",
  "Spine Surgery", "Cranial Surgery", "Functional Neurosurgery",
  "Cosmetic Dermatology", "Pediatric Dermatology", "Dermatosurgery",
  "Head & Neck Surgery", "Rhinology", "Otology", "Laryngology",
  "Andrology", "Endourology", "Uro-Oncology",
  "Dialysis", "Transplant Nephrology", "Hypertension",
  "Hepatology", "Therapeutic Endoscopy", "Inflammatory Bowel Disease",
  "Medical Oncology", "Surgical Oncology", "Radiation Oncology",
  "Diagnostic Radiology", "Interventional Radiology", "Neuroradiology",
  "Pain Management", "Critical Care", "Neuroanesthesia",
  "Child Psychiatry", "Addiction Medicine", "Geriatric Psychiatry",
  "Sleep Medicine", "Interventional Pulmonology", "Allergy",
  "Clinical Pathology", "Histopathology", "Cytopathology",
  "Orthodontics", "Endodontics", "Prosthodontics", "Periodontics",
  "Trauma Care", "Toxicology", "Disaster Medicine",
  "Retina", "Cornea", "Glaucoma", "Pediatric Ophthalmology",
  "Physiotherapy", "Occupational Therapy", "Neuro Rehab",
  "Ayurveda (Kayachikitsa, Panchakarma)", "Homeopathy", "Unani"
];

const getFullName = (doctor) => [doctor.firstName, doctor.middleName, doctor.lastName].filter(Boolean).join(" ");

type TextInputProps = {
  label: string;
  name: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  readOnly?: boolean;
  error?: string;
};

const TextInput: React.FC<TextInputProps> = ({
  label,
  name,
  value,
  onChange,
  type = "text",
  onKeyDown,
  onBlur,
  readOnly = false,
  error
}) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <input
      type={type}
      name={name}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      onBlur={onBlur}
      readOnly={readOnly}
      className={`w-full px-4 py-2 border rounded-md shadow-sm focus:outline-none ${error ? "border-red-500" : "border-gray-300"} bg-white`}
    />
    {error && <p className="text-sm text-red-600">{error}</p>}
  </div>
);
const getQualificationOptions = (role) => qualificationMap[role] || [];

const SelectInput = ({ label, name, value, onChange, options, error }) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <select
      name={name}
      value={value}
      onChange={onChange}
      className={`w-full px-4 py-2 border rounded-md shadow-sm bg-white ${error ? "border-red-500" : "border-gray-300"}`}
    >
      <option value="">Select</option>
      {options.map(opt => (
        <option key={opt} value={opt}>{opt}</option>
      ))}
    </select>
    {error && <p className="text-sm text-red-600">{error}</p>}
  </div>
);

const SearchableSelect = ({ label, name, value, onChange, options }) => {
  const [query, setQuery] = useState("");
  const filtered = options.filter(opt => opt.toLowerCase().includes(query.toLowerCase()));

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search..."
        className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
      />
      <select
        name={name}
        value={value}
        onChange={(e) => { onChange(e); setQuery(""); }}
        className="w-full mt-1 px-4 py-2 border border-gray-300 rounded-md bg-white"
      >
        <option value="">Select</option>
        {filtered.map(opt => (
          <option key={opt} value={opt}>{opt}</option>
        ))}
      </select>
    </div>
  );
};

const SectionCard = ({ title, color = "blue", children }) => {
  const colorMap = {
    blue: "border-blue-500 bg-blue-50",
    purple: "border-purple-500 bg-purple-50",
    green: "border-green-500 bg-green-50",
    yellow: "border-yellow-500 bg-yellow-50",
  };
  return (
    <div className="rounded-xl border border-gray-200 shadow-sm bg-white">
      <div className={`px-4 py-2 border-l-4 ${colorMap[color]}`}>
        <h2 className="text-base font-semibold text-gray-800">{title}</h2>
      </div>
      <div className="p-4">{children}</div>
    </div>
  );
};

// --- Zod Schemas ---
const addressSchema = z.object({
  addressLine1: z.string().min(1, "Address Line 1 is required"),
  addressLine2: z.string().optional(),
  // Use validateLetters for City, State, Country
  city: z.string().min(1, "City is required").refine(val => validateLetters()(val), { message: "Invalid characters in City" }),
  state: z.string().min(1, "State is required").refine(val => validateLetters()(val), { message: "Invalid characters in State" }),
  // Use validatePostalCode for postal code
  postalCode: z.string().min(1, "Postal Code is required").refine(val => validatePostalCode(val), { message: "Invalid Postal Code" }),
  country: z.string().min(1, "Country is required").refine(val => validateLetters()(val), { message: "Invalid characters in Country" }),
});

const doctorSchema = z.object({
  firstName: z.string().min(1, "First Name is required").refine(val => validateLettersWithSpecialChars(20)(val), { message: "Invalid characters in First Name" }),
  middleName: z.string().optional().refine(val => val === "" || validateLettersWithSpecialChars(20)(val), { message: "Invalid characters in Middle Name" }),
  lastName: z.string().min(1, "Last Name is required").refine(val => validateLettersWithSpecialChars(20)(val), { message: "Invalid characters in Last Name" }),
  gender: z.string().min(1, "Gender is required"),
  dateOfBirth: z.string().min(1, "Date of Birth is required"),
  age: z.string().optional(), // Age is calculated, so optional for schema
  // Use validateMobileNumber for mobileNumber
  mobileNumber: z.string().min(1, "Mobile Number is required").refine(val => validateMobileNumber(val), { message: "Invalid Mobile Number format" }),
  email: z.string().min(1, "Email is required").email("Invalid email address").refine(val => validateEmailCharactersOnly(30)(val), { message: "Invalid characters in Email" }),
  // Assuming registrationNumber and registrationState are alphanumeric/letters
  registrationNumber: z.string().optional().refine(val => val === "" || validateAlphaNumericWithPolicyChars(20)(val), { message: "Invalid characters in License Number" }),
  registrationState: z.string().optional().refine(val => val === "" || validateLetters(20)(val), { message: "Invalid characters in Registration State" }),
  yearsOfExperience: z.preprocess((val) => {
    // Ensure it handles empty strings or non-numeric input gracefully
    if (typeof val !== 'string' || val.trim() === '') return 0;
    const num = parseInt(val, 10);
    return isNaN(num) ? 0 : num; // Default to 0 if parsing fails
  }, z.number().min(0, "Years of Experience cannot be negative")),
  telemedicineReady: z.boolean(),
  languagesSpoken: z.array(z.string()).min(1, "At least one language must be selected"),
  isActive: z.boolean(),
  specializations: z.array(z.string()).optional(),
  primarySpecialization: z.string().optional(),
  roleType: z.string().min(1, "Role Type is required"),
  qualification: z.string().min(1, "Qualification is required"),
  address: z.array(addressSchema).min(1, "At least one address is required"),
});

const DoctorFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);
  const [formErrors, setFormErrors] = useState({});

  const [form, setForm] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    gender: "",
    dateOfBirth: "",
    age: "",
    mobileNumber: "",
    email: "",
    registrationNumber: "",
    registrationState: "",
 yearsOfExperience: 0,
    telemedicineReady: false,
    languagesSpoken: [],
    isActive: true,
    specializations: [] as string[],
    primarySpecialization: "",
    roleType: "",
    qualification: "",
    address: [
      {
        addressLine1: '',
        addressLine2: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'India'
      }
    ]
  });

  // Effect to calculate age from dateOfBirth
  useEffect(() => {
    if (form.dateOfBirth) {
      const birthDate = new Date(form.dateOfBirth);
      const today = new Date();

      let years = today.getFullYear() - birthDate.getFullYear();
      let months = today.getMonth() - birthDate.getMonth();
      let days = today.getDate() - birthDate.getDate();

      if (days < 0) {
        months--;
        const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        days += prevMonth.getDate();
      }

      if (months < 0) {
        months += 12;
        years--;
      }

      const formattedAge = `${years}y ${months}m ${days}d`;

      if (form.age !== formattedAge) {
        setForm(prev => ({ ...prev, age: formattedAge }));
      }
    }
  }, [form.dateOfBirth]);

  // Effect to load doctor data if in edit mode
  useEffect(() => {
    if (!isEdit || !id) return;

    const loadDoctor = async () => {
      try {
        const response = await getDoctorById(id);

        if (response.success && response.data) {
          const doctor = response.data;

          setForm({
            firstName: doctor.firstName || "",
            middleName: doctor.middleName || "",
            lastName: doctor.lastName || "",
            gender: doctor.gender || "",
            dateOfBirth: doctor.dateOfBirth || "",
            age: doctor.age,
            mobileNumber: doctor.mobileNumber || "",
            email: doctor.email || "",
            registrationNumber: doctor.registrationNumber || "",
            registrationState: doctor.registrationState || "",
            yearsOfExperience: doctor.yearsOfExperience || 0,
            telemedicineReady: doctor.telemedicineReady ?? false,
            languagesSpoken: doctor.languagesSpoken?.length > 0 ? doctor.languagesSpoken : [""],
            isActive: doctor.isActive ?? true,
            roleType: doctor.roleType || "",
            qualification: doctor.qualification || "",
            specializations: Array.isArray(doctor.specializations) ? doctor.specializations : [],
            primarySpecialization: doctor.primarySpecialization || "",
            address: Array.isArray(doctor.address) && doctor.address.length > 0
              ? doctor.address.map((addr) => ({
                addressLine1: addr.addressLine1 || "",
                addressLine2: addr.addressLine2 || "",
                city: addr.city || "",
                state: addr.state || "",
                postalCode: addr.postalCode || "",
                country: addr.country || "India"
              }))
              : [
                {
                  addressLine1: "",
                  addressLine2: "",
                  city: "",
                  state: "",
                  postalCode: "",
                  country: "India"
                }
              ]
          });
        } else {
          toast.error(`Failed to load doctor: ${response.error}`);
        }
      } catch (error) {
        toast.error("Failed to fetch doctor details. " + (error.message || ""));
      }
    };

    loadDoctor();
  }, [id, isEdit]);

  // Handler for specialization multi-select
 const handleSpecializationChange = (e: { target: { name: string; value: string[] } }) => {
  const { name, value } = e.target;
  setForm(prev => ({
    ...prev,
    [name]: value,
    primarySpecialization: value.includes(form.primarySpecialization) ? form.primarySpecialization : ""
  }));
  setFormErrors(prev => {
    const updated = { ...prev };
    delete updated[name];
    delete updated["primarySpecialization"];
    return updated;
  });
};


  // Handler to add a new address block
  const handleAddAddress = () => {
    setForm((prevForm) => ({
      ...prevForm,
      address: [
        ...prevForm.address,
        {
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          postalCode: "",
          country: "India"
        }
      ]
    }));
  };

  // Handler to remove an address block
  const handleRemoveAddress = (indexToRemove: number) => {
    setForm((prevForm) => ({
      ...prevForm,
      address: prevForm.address.filter((_, index) => index !== indexToRemove)
    }));
  };

  // Handler for primary specialization select change
  const handlePrimarySpecializationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setForm(prev => ({
      ...prev,
      primarySpecialization: e.target.value
    }));
  };

  // General form change handler
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
      setFormErrors(prevErrors => {
    const updatedErrors = { ...prevErrors };
    delete updatedErrors[name];
    return updatedErrors;
  });

    // Special handling for nested address fields
    if (name.startsWith("address.")) {
      const parts = name.split(".");
      const index = parseInt(parts[1], 10);
      const field = parts[2];

      setForm((prev) => {
        const updatedaddress = [...prev.address];
        updatedaddress[index] = {
          ...updatedaddress[index],
          [field]: value
        };
        return { ...prev, address: updatedaddress };
      });
    } else {
      // Standard field update
      setForm((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value
      }));
    }
  };

  // Language handlers (if using separate add/remove buttons or custom logic)
  const handleLanguageChange = (index, value) => {
    const updated = [...form.languagesSpoken];
    updated[index] = value;
    setForm(prev => ({ ...prev, languagesSpoken: updated }));
  };

  const addLanguage = () => {
    setForm(prev => ({ ...prev, languagesSpoken: [...prev.languagesSpoken, ""] }));
  };

  const removeLanguage = (index) => {
    const filtered = form.languagesSpoken.filter((_, i) => i !== index);
    setForm(prev => ({ ...prev, languagesSpoken: filtered }));
  };

  // Form submission handler
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();

  // Zod Validation
  const validationResult = doctorSchema.safeParse(form);
  if (!validationResult.success) {
    const errorMap = {};
    validationResult.error.errors.forEach((err) => {
      const fieldPath = err.path.join(".");
      errorMap[fieldPath] = err.message;
    });
    setFormErrors(errorMap);
    return;
  }
  setFormErrors({}); 

  const isDoctor = form.roleType === "Doctor";

  // Validate primary specialization if Doctor
  if (isDoctor && form.specializations.length > 0) {
    if (
      !form.primarySpecialization ||
      !form.specializations.includes(form.primarySpecialization)
    ) {
      toast.error("Please select a valid primary specialization.");
      return;
    }
  }

  try {
    // Construct payload with properly typed and role-filtered values
    const payload = {
      ...form,
      // Ensure number; fallback to 0 if empty

      qualification: form.qualification?.toString() || null, // Normalize to string or null

      // Clear doctor-specific fields if not a doctor
      registrationNumber: isDoctor ? form.registrationNumber || null : null,
      registrationState: isDoctor ? form.registrationState || null : null,
      specializations: isDoctor ? form.specializations : [],
      primarySpecialization: isDoctor ? form.primarySpecialization || null : null,

      // Facility from local storage (can be moved to a hook if reused)
      facilityId: localStorage.getItem("Facility") || "unknown",
    };

    const response = isEdit && id
      ? await updateDoctor(id, payload)
      : await createDoctor(payload);

    if (response.success) {
      toast.success(
        response.message || `Doctor ${isEdit ? "updated" : "created"} successfully.`
      );
      setTimeout(() => navigate("/doctors"), 500);
    } else {
      // Handle API errors (e.g. 409 Conflict or others)
      const errorParts = response.error?.split(":", 2);
      const statusCode = errorParts?.[0];
      const errorMessage = errorParts?.[1] || response.error;

      if (statusCode === "409") {
        toast.error("A doctor with similar details already exists.");
      } else {
        toast.error(errorMessage || "Something went wrong while saving the doctor.");
      }
    }
  } catch (error: any) {
    if (error?.response?.status === 409) {
      toast.error("A doctor with similar details already exists.");
    } else {
      toast.error("An unexpected error occurred: " + (error.message || ""));
    }
  }
};


  // Handler for the multi-select languages input
  const handleLanguageSelect = (e: { target: { name: string; value: string[] } }) => {
  const { name, value } = e.target;
  setForm(prev => ({ ...prev, [name]: value }));
  setFormErrors(prev => {
    const updated = { ...prev };
    delete updated[name];
    return updated;
  });
};

  // --- JSX Rendering ---
  return (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div className="max-w-5xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold text-center text-gray-800 mb-8">
        {isEdit ? "Edit Practitioner" : "Register Practitioner"}
      </h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <SectionCard title="Personal Information" color="blue">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="First Name"
              name="firstName"
              value={form.firstName}
              onChange={handleChange}
              onKeyDown={allowOnlyLettersWithSpecialChars(20)}
              error={formErrors["firstName"]}
            />
            <TextInput
              label="Middle Name"
              name="middleName"
              value={form.middleName}
              onChange={handleChange}
              onKeyDown={allowOnlyLettersWithSpecialChars(20)}
              error={formErrors["middleName"]}
            />
            <TextInput
              label="Last Name"
              name="lastName"
              value={form.lastName}
              onChange={handleChange}
              onKeyDown={allowOnlyLettersWithSpecialChars(20)}
              error={formErrors["lastName"]}
            />
            <SelectInput
              label="Gender"
              name="gender"
              value={form.gender}
              onChange={handleChange}
              options={["Male", "Female", "Other", "Unknown"]}
              error={formErrors["gender"]}
            />
            <TextInput
              label="Date of Birth"
              name="dateOfBirth"
              type="date"
              value={form.dateOfBirth}
              onChange={handleChange}
              error={formErrors["dateOfBirth"]}
            />
            <TextInput
              label="Age"
              name="age"
              type="text"
              value={form.age}
              onChange={() => {}}
              readOnly
            />
            <TextInput
              label="Mobile Number"
              name="mobileNumber"
              value={form.mobileNumber}
              onChange={handleChange}
              onKeyDown={allowOnlyNumbersWithLimit(10)}
              error={formErrors["mobileNumber"]}
            />
            <TextInput
              label="Email"
              name="email"
              type="email"
              value={form.email}
              onChange={handleChange}
              onKeyDown={allowEmailCharactersOnly(30)}
              error={formErrors["email"]}
            />
          </div>
        </SectionCard>

        <SectionCard title="Professional Information" color="purple">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="Role Type"
              name="roleType"
              value={form.roleType}
              onChange={handleChange}
              options={ROLE_TYPES}
              error={formErrors["roleType"]}
            />

            {form.roleType !== "" && (
              ["Patient", "Guest"].includes(form.roleType) ? (
                <TextInput
                  label="Qualification"
                  name="qualification"
                  value={form.qualification}
                  onChange={handleChange}
                  error={formErrors["qualification"]}
                />
              ) : (
                <SelectInput
                  label="Qualification"
                  name="qualification"
                  value={form.qualification}
                  onChange={handleChange}
                  options={getQualificationOptions(form.roleType)}
                  error={formErrors["qualification"]}
                />
              )
            )}

            {form.roleType === "Doctor" && (
              <>
                <TextInput
                  label="License Number"
                  name="registrationNumber"
                  value={form.registrationNumber}
                  onChange={handleChange}
                  onKeyDown={allowAlphaNumericWithPolicyChars(20)}
                  error={formErrors["registrationNumber"]}
                />
                <TextInput
                  label="Registration State"
                  name="registrationState"
                  value={form.registrationState}
                  onChange={handleChange}
                  onKeyDown={allowOnlyLetters(20)}
                  error={formErrors["registrationState"]}
                />
              </>
            )}

            <TextInput
              label="Years of Experience"
              name="yearsOfExperience"
              type="number"
              value={form.yearsOfExperience}
              onChange={handleChange}
              onKeyDown={(e) => {
                if (["e", "E", "+", "-"].includes(e.key)) e.preventDefault();
                const isControlKey = ["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key);
                if (!isControlKey && e.currentTarget.value?.length >= 3) e.preventDefault();
              }}
              error={formErrors["yearsOfExperience"]}
            />

            <ReactSelectInput
              label="Languages Spoken"
              name="languagesSpoken"
              value={form.languagesSpoken}
              onChange={handleLanguageSelect}
              options={languageOptions}
              isMulti={true}
              error={formErrors["languagesSpoken"]}
            />
          </div>

          {form.roleType === "Doctor" && (
            <div className="mt-4">
              <SectionCard title="Specialization Details" color="purple">
                <ReactSelectInput
                  label="Specializations"
                  name="specializations"
                  value={form.specializations}
                  onChange={handleSpecializationChange}
                  options={SPECIALIZATIONS}
                  isMulti={true}
                  error={formErrors["specializations"]}
                />

                {form.specializations.length > 0 && (
                  <div className="mt-8 space-y-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Primary Specialization
                    </label>
                    <select
                      name="primarySpecialization"
                      value={form.primarySpecialization}
                      onChange={handlePrimarySpecializationChange}
                      className={`w-full border rounded-md px-4 py-2 bg-white ${formErrors["primarySpecialization"] ? "border-red-500" : "border-gray-300"}`}
                    >
                      <option value="" disabled>Select primary</option>
                      {form.specializations.map((spec) => (
                        <option key={spec} value={spec}>{spec}</option>
                      ))}
                    </select>
                    {formErrors["primarySpecialization"] && (
                      <p className="text-sm text-red-600">{formErrors["primarySpecialization"]}</p>
                    )}
                  </div>
                )}
              </SectionCard>
            </div>
          )}
        </SectionCard>

        <SectionCard title="Address" color="green">
          {form.address.map((address, index) => (
            <SectionCard key={index} title={`Address ${index + 1}`} color="green">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <TextInput
                  label="Address Line1"
                  name={`address.${index}.addressLine1`}
                  value={address.addressLine1}
                  onChange={handleChange}
                  onKeyDown={allowAlphaNumericWithPolicyChars(50)}
                  error={formErrors[`address.${index}.addressLine1`]}
                />
                <TextInput
                  label="Address Line 2"
                  name={`address.${index}.addressLine2`}
                  value={address.addressLine2}
                  onChange={handleChange}
                  onKeyDown={allowAlphaNumericWithPolicyChars(50)}
                  error={formErrors[`address.${index}.addressLine2`]}
                />
                <TextInput
                  label="City"
                  name={`address.${index}.city`}
                  value={address.city}
                  onChange={handleChange}
                  onKeyDown={allowOnlyLetters(30)}
                  error={formErrors[`address.${index}.city`]}
                />
                <TextInput
                  label="State"
                  name={`address.${index}.state`}
                  value={address.state}
                  onChange={handleChange}
                  onKeyDown={allowOnlyLetters(30)}
                  error={formErrors[`address.${index}.state`]}
                />
                <TextInput
                  label="Postal Code"
                  name={`address.${index}.postalCode`}
                  value={address.postalCode}
                  onChange={handleChange}
                  onKeyDown={digitsIncludingZero(6)}
                  error={formErrors[`address.${index}.postalCode`]}
                />
                <TextInput
                  label="Country"
                  name={`address.${index}.country`}
                  value={address.country}
                  onChange={handleChange}
                  onKeyDown={allowOnlyLetters(30)}
                  error={formErrors[`address.${index}.country`]}
                />
              </div>

              <div className="mt-2 flex justify-end">
                {form.address.length > 1 && (
                  <button
                    type="button"
                    className="text-red-600 text-sm"
                    onClick={() => handleRemoveAddress(index)}
                  >
                    Remove Address
                  </button>
                )}
              </div>
            </SectionCard>
          ))}

          <div className="mt-4">
            <button
              type="button"
              onClick={handleAddAddress}
              className="px-4 py-2 bg-green-600 text-white rounded"
            >
              Add Address
            </button>
          </div>
        </SectionCard>

        <SectionCard title="Status" color="yellow">
          <div className="flex items-center gap-6">
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="telemedicineReady" checked={form.telemedicineReady} onChange={handleChange} />
              Telemedicine Ready
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="isActive" checked={form.isActive} onChange={handleChange} />
              Active
            </label>
          </div>
        </SectionCard>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <button
            type="button"
            onClick={() => navigate("/doctors")}
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg shadow hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow hover:from-blue-700 hover:to-indigo-700"
          >
            {isEdit ? "Save Changes" : "Register Doctor"}
          </button>
        </div>
      </form>
    </div>
  </div>
);

};

export default DoctorFormPage;

