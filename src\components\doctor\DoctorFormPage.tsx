import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getDoctorById, createDoctor, updateDoctor } from "../../services/doctorApis";
import { ReactSelectInput } from "../../commonfields/ReactSelectInput";
import {allowOnlyLettersWithSpecialChars,allowOnlyNumbersWithLimit,allowOnlyLetters,allowAlphaNumericWithPolicyChars,allowEmailCharactersOnly} from "../../inputhelpers/inputHelpers"

// ADD THIS at the top of the file (below existing imports)

// languages.ts (or wherever you store constants)
export const languageOptions = [
  // General Indian Languages
  "Hindi",
  "English",
  "Bengali",
  "Telugu",
  "Khasi",
  "Garo",
  "Pnar",
  "Marathi",
  "Tamil",
  "Urdu",
  "Gujarati",
  "Kannada",
  "Odia",
  "Malayalam",
  "Punjabi",
  "Assamese",
  "Maithili",
  "Santali",
  "Konkani",
  "Manipuri",
  "Kashmiri",
  "Dogri",
  "Sindhi",
  "Bodo",
  "Nepali",
  "Sanskrit",


  
   
];


const qualificationMap = {
  "Administrator": [
    "MBA (Hospital Administration)",
    "MHA (Health Administration)",
    "Diploma in Hospital Management", "Others"
  ],
  "Doctor": [
    "MBBS",
    "MD (General Medicine)",
    "MS (General Surgery)",
    "DNB (Diplomate of National Board)",
    "DM (Cardiology)",
    "DM (Neurology)",
    "MCh (Cardiothoracic Surgery)",
    "DO (Doctor of Osteopathy)",
    "BAMS (Ayurveda)",
    "BHMS (Homeopathy)",
    "BUMS (Unani)",
    "MD (Ayurveda)",
    "MD (Homeopathy)",
    "PhD in Medicine",
    "Fellowship in Oncology",
    "Fellowship in Gastroenterology"
  ],
  "FrontOffice": [
    "Diploma in Hospital Management",
    "Certificate in Medical Receptionist Training",
    "BHM (Bachelor in Hospital Management)", "Others"
  ],
  "Report User": [
    "B.Sc (Medical Lab Technology)",
    "DMLT (Diploma in Medical Lab Technology)",
    "B.Sc (Radiology)",
    "Diploma in Radiology"
  ],
  "Therapist": [
    "BPT (Bachelor of Physiotherapy)",
    "MPT (Master of Physiotherapy)",
    "BOT (Bachelor of Occupational Therapy)",
    "MOT (Master of Occupational Therapy)",
    "BASLP (Audiology & Speech Language Pathology)",
    "Diploma in Occupational Therapy"
  ],
  "Patient": [],
  "Pharmacist": [
    "D.Pharm (Diploma in Pharmacy)",
    "B.Pharm (Bachelor of Pharmacy)",
    "M.Pharm (Master of Pharmacy)"
  ],
  "Clinic Manager": [
    "MBA (Healthcare Management)",
    "BHM (Bachelor of Hospital Management)",
    "Diploma in Clinic Administration"
  ],
  "Guest": [],
  "Counsellor": [
    "MA (Psychology)",
    "MSW (Master of Social Work - Medical & Psychiatry)",
    "Diploma in Mental Health Counselling",
    "Others"
  ],
  "Moderator": [
    "B.Sc (Health Information Management)",
    "Certificate in Healthcare IT",
    "Diploma in Medical Records Management"
  ]
};

const ROLE_TYPES = [
  "Administrator", "Doctor", "FrontOffice", "Report User", "Therapist", "Patient",
  "Pharmacist", "Clinic Manager", "Guest", "Counsellor", "Moderator"
];


const SPECIALIZATIONS = [
  "Internal Medicine", "Diabetology", "Infectious Diseases", "Geriatrics",
  "Laparoscopic Surgery", "Breast Surgery", "Trauma Surgery",
  "Joint Replacement", "Spine Surgery", "Sports Medicine",
  "Pediatric Neurology", "Pediatric Cardiology", "Neonatology",
  "Maternal-Fetal Medicine", "Infertility", "Gynecologic Oncology",
  "Interventional Cardiology", "Electrophysiology", "Non-Invasive Cardiology",
  "Stroke", "Epilepsy", "Neurophysiology",
  "Spine Surgery", "Cranial Surgery", "Functional Neurosurgery",
  "Cosmetic Dermatology", "Pediatric Dermatology", "Dermatosurgery",
  "Head & Neck Surgery", "Rhinology", "Otology", "Laryngology",
  "Andrology", "Endourology", "Uro-Oncology",
  "Dialysis", "Transplant Nephrology", "Hypertension",
  "Hepatology", "Therapeutic Endoscopy", "Inflammatory Bowel Disease",
  "Medical Oncology", "Surgical Oncology", "Radiation Oncology",
  "Diagnostic Radiology", "Interventional Radiology", "Neuroradiology",
  "Pain Management", "Critical Care", "Neuroanesthesia",
  "Child Psychiatry", "Addiction Medicine", "Geriatric Psychiatry",
  "Sleep Medicine", "Interventional Pulmonology", "Allergy",
  "Clinical Pathology", "Histopathology", "Cytopathology",
  "Orthodontics", "Endodontics", "Prosthodontics", "Periodontics",
  "Trauma Care", "Toxicology", "Disaster Medicine",
  "Retina", "Cornea", "Glaucoma", "Pediatric Ophthalmology",
  "Physiotherapy", "Occupational Therapy", "Neuro Rehab",
  "Ayurveda (Kayachikitsa, Panchakarma)", "Homeopathy", "Unani"
];

const getFullName = (doctor) => [doctor.firstName, doctor.middleName, doctor.lastName].filter(Boolean).join(" ");



type TextInputProps = {
  label: string;
  name: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  readOnly?: boolean; // ✅ Add this line
};

const TextInput: React.FC<TextInputProps> = ({
  label,
  name,
  value,
  onChange,
  type = "text",
  onKeyDown,
  onBlur,
  readOnly = false // ✅ Add default value
}) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <input
      type={type}
      name={name}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      onBlur={onBlur}
      readOnly={readOnly} // ✅ Apply here
      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
    />
  </div>
);



const getQualificationOptions = (role) => qualificationMap[role] || [];

const SelectInput = ({ label, name, value, onChange, options }) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <select
      name={name}
      value={value}
      onChange={onChange}
      required
      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
    >
      <option value="">Select</option>
      {options.map(opt => (
        <option key={opt} value={opt}>{opt}</option>
      ))}
    </select>
  </div>
);

const SearchableSelect = ({ label, name, value, onChange, options }) => {
  const [query, setQuery] = useState("");
  const filtered = options.filter(opt => opt.toLowerCase().includes(query.toLowerCase()));

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search..."
        className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
      />
      <select
        name={name}
        value={value}
        onChange={(e) => { onChange(e); setQuery(""); }}
        className="w-full mt-1 px-4 py-2 border border-gray-300 rounded-md bg-white"
      >
        <option value="">Select</option>
        {filtered.map(opt => (
          <option key={opt} value={opt}>{opt}</option>
        ))}
      </select>
    </div>
  );
};

const SectionCard = ({ title, color = "blue", children }) => {
  const colorMap = {
    blue: "border-blue-500 bg-blue-50",
    purple: "border-purple-500 bg-purple-50",
    green: "border-green-500 bg-green-50",
    yellow: "border-yellow-500 bg-yellow-50",
  };
  return (
    <div className="rounded-xl border border-gray-200 shadow-sm bg-white">
      <div className={`px-4 py-2 border-l-4 ${colorMap[color]}`}>
        <h2 className="text-base font-semibold text-gray-800">{title}</h2>
      </div>
      <div className="p-4">{children}</div>
    </div>
  );
};

const DoctorFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);


  const [form, setForm] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    gender: "",
    dateOfBirth: "",
    age:"",
    mobileNumber: "",
    email: "",
    registrationNumber: "",
    registrationState: "",
    yearsOfExperience: 0,
    telemedicineReady: false,
     languagesSpoken: [],
    isActive: true,
    specializations: [] as string[],
  primarySpecialization: "",
    roleType: "",
    qualification: "",
   address: [
    {
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'India'
    }
  ]

  });
  

  useEffect(() => {
        if (form.dateOfBirth) {
            const birthDate = new Date(form.dateOfBirth);
            const today = new Date();
 
            let years = today.getFullYear() - birthDate.getFullYear();
            let months = today.getMonth() - birthDate.getMonth();
            let days = today.getDate() - birthDate.getDate();
 
            if (days < 0) {
                // Go to previous month
                months--;
                const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                days += prevMonth.getDate();
            }
 
            if (months < 0) {
                months += 12;
                years--;
            }
 
            const formattedAge = `${years}y ${months}m ${days}d`;
 
            if (form.age !== formattedAge) {
                setForm(prev => ({ ...prev, age: formattedAge }));
            }
        }
    }, [form.dateOfBirth]);
 


  useEffect(() => {
  if (!isEdit || !id) return;

  const loadDoctor = async () => {
    try {
      const response = await getDoctorById(id);

      if (response.success && response.data) {
        const doctor = response.data;

        setForm({
          firstName: doctor.firstName || "",
          middleName: doctor.middleName || "",
          lastName: doctor.lastName || "",
          gender: doctor.gender || "",
          dateOfBirth: doctor.dateOfBirth || "",
          age:doctor.age,
          mobileNumber: doctor.mobileNumber || "",
          email: doctor.email || "",
          registrationNumber: doctor.registrationNumber || "",
          registrationState: doctor.registrationState || "",
          yearsOfExperience: doctor.yearsOfExperience || 0,
          telemedicineReady: doctor.telemedicineReady ?? false,
          languagesSpoken: doctor.languagesSpoken?.length > 0 ? doctor.languagesSpoken : [""],
          isActive: doctor.isActive ?? true,
          roleType: doctor.roleType || "",
          qualification: doctor.qualification || "",
          specializations: Array.isArray(doctor.specializations) ? doctor.specializations : [],
          primarySpecialization: doctor.primarySpecialization || "",
          address: Array.isArray(doctor.address) && doctor.address.length > 0
            ? doctor.address.map((addr) => ({
                addressLine1: addr.addressLine1 || "",
                addressLine2: addr.addressLine2 || "",
                city: addr.city || "",
                state: addr.state || "",
                postalCode: addr.postalCode || "",
                country: addr.country || "India"
              }))
            : [
                {
                  addressLine1: "",
                  addressLine2: "",
                  city: "",
                  state: "",
                  postalCode: "",
                  country: "India"
                }
              ]
        });
      } else {
        toast.error(`Failed to load doctor: ${response.error}`);
      }
    } catch (error) {
      toast.error("Failed to fetch doctor details. " + (error.message || ""));
    }
  };

  loadDoctor();
}, [id, isEdit]);



  // ADD INSIDE THE COMPONENT

const handleSpecializationChange = (e: { target: { name: string; value: string[] } }) => {
  const { name, value } = e.target;
  const updatedPrimary = value.includes(form.primarySpecialization)
    ? form.primarySpecialization
    : "";
  setForm(prev => ({
    ...prev,
    [name]: value,
    primarySpecialization: updatedPrimary
  }));
};

// Add a new empty address to the list
const handleAddAddress = () => {
  setForm((prevForm) => ({
    ...prevForm,
    address: [
      ...prevForm.address,
      {
        addressLine1: "",
        addressLine2: "",
        city: "",
        state: "",
        postalCode: "",
        country: "India"
      }
    ]
  }));
};

// Remove an address at a specific index
const handleRemoveAddress = (indexToRemove: number) => {
  setForm((prevForm) => ({
    ...prevForm,
    address: prevForm.address.filter((_, index) => index !== indexToRemove)
  }));
};


const handlePrimarySpecializationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
  setForm(prev => ({
    ...prev,
    primarySpecialization: e.target.value
  }));
};

  const handleChange = (e) => {
  const { name, value, type, checked } = e.target;

  // Check if it's an address field
  if (name.startsWith("address.")) {
    const parts = name.split(".");
    const index = parseInt(parts[1], 10);
    const field = parts[2];

    setForm((prev) => {
      const updatedaddress = [...prev.address];
      updatedaddress[index] = {
        ...updatedaddress[index],
        [field]: value
      };
      return { ...prev, address: updatedaddress };
    });
  } else {
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  }
};

  

  const handleLanguageChange = (index, value) => {
    const updated = [...form.languagesSpoken];
    updated[index] = value;
    setForm(prev => ({ ...prev, languagesSpoken: updated }));
  };

  const addLanguage = () => {
    setForm(prev => ({ ...prev, languagesSpoken: [...prev.languagesSpoken, ""] }));
  };

  const removeLanguage = (index) => {
    const filtered = form.languagesSpoken.filter((_, i) => i !== index);
    setForm(prev => ({ ...prev, languagesSpoken: filtered }));
  };

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();

  // ✅ Validation for primary specialization
  const isDoctor = form.roleType === "Doctor";

  if (
    isDoctor &&
    form.specializations.length > 0 &&
    (!form.primarySpecialization || !form.specializations.includes(form.primarySpecialization))
  ) {
    toast.error("Please select a valid primary specialization.");
    return;
  }

  try {
    // Construct the payload with correct data shapes
    const payload = {
      ...form,

      qualification: String(form.qualification),

      // ✅ Backend now expects array for specialization
      

      // ✅ Send primary specialization if role is Doctor
      primarySpecialization: isDoctor ? form.primarySpecialization : "N/A",

      // ✅ Send registration number only for Doctors
      registrationNumber: isDoctor ? form.registrationNumber : "N/A",
      facilityId: localStorage.getItem("Facility") || "unknown",


      // Optional: clear specialization/primary if not a Doctor
      // You can skip if backend tolerates empty/NA
    };
console.log(payload);
    const response = isEdit && id
      ? await updateDoctor(id, payload)
      : await createDoctor(payload);

    if (response.success) {
      toast.success(
        response.message || `Doctor ${isEdit ? "updated" : "created"} successfully.`
      );
      setTimeout(() => navigate("/doctors"), 500);
    } else {
      // Optional: split error format if it’s a string like "409: message"
      const [statusCode, errorMsg] = response.error?.split(":", 2) || [];

      if (statusCode === "409") {
        toast.error("A doctor with similar details already exists.");
      } else {
        toast.error(errorMsg || "Something went wrong while saving the doctor.");
      }
    }
  } catch (error: any) {
    if (error?.response?.status === 409) {
      toast.error("A doctor with similar details already exists.");
    } else {
      toast.error("Unexpected error: " + (error.message || "unknown error"));
    }
  }
};


const handleLanguageSelect = (e: { target: { name: string; value: string[] } }) => {
  const { name, value } = e.target;
  setForm((prev) => ({
    ...prev,
    [name]: value
  }));
};




  // The rest of the JSX rendering remains unchanged
 return (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <ToastContainer />
    <div className="max-w-5xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold text-center text-gray-800 mb-8">
        {isEdit ? "Edit Practitioner" : "Register Practitioner"}
      </h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <SectionCard title="Personal Information" color="blue">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput label="First Name" name="firstName" value={form.firstName} onChange={handleChange} onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <TextInput label="Middle Name" name="middleName" value={form.middleName} onChange={handleChange}onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <TextInput label="Last Name" name="lastName" value={form.lastName} onChange={handleChange} onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <SelectInput label="Gender" name="gender" value={form.gender} onChange={handleChange} options={["Male", "Female", "Other","Unknown"]} />
            <TextInput label="Date of Birth" name="dateOfBirth" type="date" value={form.dateOfBirth} onChange={handleChange} />
          <TextInput
  label="Age"
  name="age"
  type="text"
  value={form.age}
  onChange={() => {}} 
  readOnly// prevent manual changes
  
/>


            <TextInput label="Mobile Number" name="mobileNumber" value={form.mobileNumber} onChange={handleChange} onKeyDown={allowOnlyNumbersWithLimit(10)} />
            <TextInput label="Email" name="email" type="email" value={form.email} onChange={handleChange} onKeyDown={allowEmailCharactersOnly(30)} />
          </div>
        </SectionCard>
<SectionCard title="Professional Information" color="purple">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-0"> {/* removed default spacing */}
    <SelectInput
      label="Role Type"
      name="roleType"
      value={form.roleType}
      onChange={handleChange}
      options={ROLE_TYPES}
    />
     {form.roleType !== "" && (
      ["Patient", "Guest"].includes(form.roleType) ? (
        <TextInput
          label="Qualification"
          name="qualification"
          value={form.qualification}
          onChange={handleChange}
        />
      ) : (
        <SelectInput
          label="Qualification"
          name="qualification"
          value={form.qualification}
          onChange={handleChange}
          options={getQualificationOptions(form.roleType)}
        />
      )
    )}

    {form.roleType === "Doctor" && (
      <>
        <TextInput
          label="License Number"
          name="registrationNumber"
          value={form.registrationNumber}
          onChange={handleChange}
          onKeyDown={allowAlphaNumericWithPolicyChars()}
        />
        <TextInput
          label="Registration State"
          name="registrationState"
          value={form.registrationState}
          onChange={handleChange}
          onKeyDown={allowOnlyLetters()}
        />
      </>
    )}

    <TextInput
      label="Years of Experience"
      name="yearsOfExperience"
      type="number"
      value={form.yearsOfExperience}
      onChange={(e) => {
        const value = parseInt(e.target.value, 10);
        if (value >= 0 || e.target.value === "") {
          handleChange(e);
        }
      }}
      onKeyDown={(e) => {
        if (["e", "E", "+", "-"].includes(e.key)) {
          e.preventDefault();
        }

        const inputValue = e.currentTarget.value;
        const isControlKey = ["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key);
        if (!isControlKey && inputValue?.length >= 2) {
          e.preventDefault();
        }
      }}
    />

   

    <ReactSelectInput
      label="Languages Spoken"
      name="languagesSpoken"
      value={form.languagesSpoken}
      onChange={handleLanguageSelect}
      options={languageOptions}
      isMulti={true}
    />
  </div>

  {/* Specialization section with tighter spacing */}
  {form.roleType === "Doctor" && (
    <div className="mt-2"> {/* reduce top margin here */}
      <SectionCard title="Specialization Details" color="purple">
        <ReactSelectInput
          label="Specializations"
          name="specializations"
          value={form.specializations}
          onChange={handleSpecializationChange}
          options={SPECIALIZATIONS}
          isMulti={true}
        />

        {form.specializations.length > 0 && (
          <div className="mt-8 space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              Primary Specialization
            </label>
            <select
              name="primarySpecialization"
              value={form.primarySpecialization}
              onChange={handlePrimarySpecializationChange}
              className="w-full border border-gray-300 rounded-md px-4 py-2 bg-white"
            >
              <option value="" disabled>Select primary</option>
              {form.specializations.map((spec) => (
                <option key={spec} value={spec}>{spec}</option>
              ))}
            </select>
          </div>
        )}
      </SectionCard>
    </div>
  )}
</SectionCard>





       <SectionCard title="Address" color="green">
{form.address.map((address, index) => (
  <SectionCard key={index} title={`Address ${index + 1}`} color="green">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <TextInput
        label="Address Line1"
        name={`address.${index}.addressLine1`}
        value={address.addressLine1}
        onChange={handleChange}
        onKeyDown={allowAlphaNumericWithPolicyChars()}
      />
      <TextInput
        label="Address Line 2"
        name={`address.${index}.addressLine2`}
        value={address.addressLine2}
        onChange={handleChange}
        onKeyDown={allowAlphaNumericWithPolicyChars()}
      />
      <TextInput
        label="City"
        name={`address.${index}.city`}
        value={address.city}
        onChange={handleChange}
        onKeyDown={allowOnlyLetters()}
      />
      <TextInput
        label="State"
        name={`address.${index}.state`}
        value={address.state}
        onChange={handleChange}
        onKeyDown={allowOnlyLetters()}
      />
      <TextInput
        label="Postal Code"
        name={`address.${index}.postalCode`}
        value={address.postalCode}
        onChange={handleChange}
        onKeyDown={allowOnlyNumbersWithLimit(6)}
      />
      <TextInput
        label="Country"
        name={`address.${index}.country`}
        value={address.country}
        onChange={handleChange}
        onKeyDown={allowOnlyLetters()}
      />
    </div>

    <div className="mt-2 flex justify-end">
      {form.address.length > 1 && (
        <button
          type="button"
          className="text-red-600 text-sm"
          onClick={() => handleRemoveAddress(index)}
        >
          Remove Address
        </button>
      )}
    </div>
  </SectionCard>
))}

<div className="mt-4">
  <button
    type="button"
    onClick={handleAddAddress}
    className="px-4 py-2 bg-green-600 text-white rounded"
  >
    Add Address
  </button>
</div>

</SectionCard>


        <SectionCard title="Status" color="yellow">
          <div className="flex items-center gap-6">
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="telemedicineReady" checked={form.telemedicineReady} onChange={handleChange} />
              Telemedicine Ready
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="isActive" checked={form.isActive} onChange={handleChange} />
              Active
            </label>
          </div>
        </SectionCard>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <button
            type="button"
            onClick={() => navigate("/doctors")}
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg shadow hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow hover:from-blue-700 hover:to-indigo-700"
          >
            {isEdit ? "Save Changes" : "Register Doctor"}
          </button>
        </div>
      </form>
    </div>
  </div>
);



};

export default DoctorFormPage;
