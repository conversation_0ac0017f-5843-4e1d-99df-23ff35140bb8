export const allowOnlyNumbersWithLimit = (maxLength: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow navigation and control keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    const isNumber = /^\d$/.test(key);
    const willExceed = input.value.length >= maxLength;

    if (!isNumber || willExceed) {
        e.preventDefault();
        return;
    }

    // Disallow 0 as the first digit
    if (input.value.length === 0 && key === "0") {
        e.preventDefault();
    }
};


export const allowOnlyLettersWithSpecialChars = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    const isAllowedChar = /^[a-zA-Z' -]$/.test(key) && !(input.value.length === 0 && !/^[a-zA-Z]$/.test(key));
    const willExceed = maxLength !== undefined && input.value.length >= maxLength;

    if (!isAllowedChar || willExceed) {
        e.preventDefault();
    }
};



export const allowOnlyLetters = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    const isLetter = /^[a-zA-Z ]$/.test(key) && !(input.value.length === 0 && !/^[a-zA-Z]$/.test(key));
    const willExceed = maxLength !== undefined && input.value.length >= maxLength;

    if (!isLetter || willExceed) {
        e.preventDefault();
    }
};



export const allowOnlyCoverageAmount = (maxLength?: number) => (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  const key = e.key;
  const input = e.currentTarget;
  const value = input.value;

  // Allow navigation and editing keys
  if (["Backspace", "Tab", "ArrowLeft", "ArrowRight", "Delete"].includes(key)) return;

  // Allow only digits and optional commas
  const isAllowedChar = /^[0-9,]$/.test(key);
  const willExceed = maxLength !== undefined && value.length >= maxLength;

  // Prevent leading zero unless followed by a comma (e.g., "0,100")
  const isFirstChar = value.length === 0;
  const isLeadingZero = isFirstChar && key === "0";

  if (!isAllowedChar || isLeadingZero || willExceed) {
    e.preventDefault();
  }
};



export const allowAlphaNumericWithPolicyChars = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;
    const value = input.value;
 
    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;
 
    // Allow letters, digits, and policy-related special characters
    const isValidChar = /^[a-zA-Z0-9/\-.]$/.test(key);
 
    // First character must be alphanumeric (not /, -, or .)
    const isFirstChar = value.length === 0;
    const isFirstCharValid = /^[a-zA-Z0-9]$/.test(key);
 
    const willExceed = maxLength !== undefined && value.length >= maxLength;
 
    if (!isValidChar || (isFirstChar && !isFirstCharValid) || willExceed) {
        e.preventDefault();
    }
};
 

export const allowEmailCharactersOnly = (maxLength?: number) => (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  const key = e.key;
  const input = e.currentTarget;
  const value = input.value;

  // Allow control/navigation keys
  if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

  // Allow typical email characters
  const isValidChar = /^[a-zA-Z0-9@._-]$/.test(key);
  if (!isValidChar) {
    e.preventDefault();
    return;
  }

  // Prevent invalid starting characters
  if (value.length === 0 && /[@._-]/.test(key)) {
    e.preventDefault();
    return;
  }

  // Prevent double "@"
  if (key === "@" && value.includes("@")) {
    e.preventDefault();
    return;
  }

  // Prevent exceeding length
  const willExceed = maxLength !== undefined && value.length >= maxLength;
  if (willExceed) {
    e.preventDefault();
  }
};
