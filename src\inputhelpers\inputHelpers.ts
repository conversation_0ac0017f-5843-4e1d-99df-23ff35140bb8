// inputHelpers/inputHelpers.ts

// --- EXISTING ONKEYDOWN HANDLERS ---

export const allowOnlyNumbersWithLimit = (maxLength: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow navigation and control keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    const isNumber = /^\d$/.test(key);
    const willExceed = input.value.length >= maxLength;

    if (!isNumber || willExceed) {
        e.preventDefault();
        return;
    }

    // Disallow 0 as the first digit
    if (input.value.length === 0 && key === "0") {
        e.preventDefault();
    }
};

export const digitsIncludingZero = (maxLength?: number) => (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  const key = e.key;
  const input = e.currentTarget;

  // Allow navigation/control keys
  if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

  const isDigit = /^\d$/.test(key);
  const willExceed = maxLength ? input.value.length >= maxLength : false;

  if (!isDigit || willExceed) {
    e.preventDefault();
  }
};

export const allowOnlyLettersWithSpecialChars = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    // Regex for letters, apostrophe, space, hyphen
    const isAllowedChar = /^[a-zA-Z' -]$/.test(key);
    // Ensure it doesn't start with a space or apostrophe
    const startsWithValidChar = !(input.value.length === 0 && (key === ' ' || key === "'"));

    const willExceed = maxLength !== undefined && input.value.length >= maxLength;

    if (!isAllowedChar || !startsWithValidChar || willExceed) {
        e.preventDefault();
    }
};

export const allowOnlyLetters = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    // Regex for letters and space
    const isLetter = /^[a-zA-Z ]$/.test(key);
    // Ensure it doesn't start with a space
    const startsWithValidChar = !(input.value.length === 0 && key === ' ');

    const willExceed = maxLength !== undefined && input.value.length >= maxLength;

    if (!isLetter || !startsWithValidChar || willExceed) {
        e.preventDefault();
    }
};

export const allowOnlyCoverageAmount = (maxLength?: number) => (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  const key = e.key;
  const input = e.currentTarget;
  const value = input.value;

  // Allow navigation and editing keys
  if (["Backspace", "Tab", "ArrowLeft", "ArrowRight", "Delete"].includes(key)) return;

  // Allow only digits and commas
  const isAllowedChar = /^[0-9,]$/.test(key);
  const willExceed = maxLength !== undefined && value.length >= maxLength;

  // Prevent leading zero unless followed by a comma (e.g., "0,100")
  const isFirstChar = value.length === 0;
  const isLeadingZero = isFirstChar && key === "0";

  if (!isAllowedChar || isLeadingZero || willExceed) {
    e.preventDefault();
  }
};

export const allowAlphaNumericWithPolicyChars = (maxLength?: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;
    const value = input.value;

    // Allow control/navigation keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    // Allow letters, digits, and policy-related special characters: /, -, .
    const isValidChar = /^[a-zA-Z0-9/\-.]$/.test(key);

    // First character must be alphanumeric (not /, -, or .)
    const isFirstChar = value.length === 0;
    const isFirstCharValid = /^[a-zA-Z0-9]$/.test(key);

    const willExceed = maxLength !== undefined && value.length >= maxLength;

    if (!isValidChar || (isFirstChar && !isFirstCharValid) || willExceed) {
        e.preventDefault();
    }
};

export const allowEmailCharactersOnly = (maxLength?: number) => (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  const key = e.key;
  const input = e.currentTarget;
  const value = input.value;

  // Allow control/navigation keys
  if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

  // Allow typical email characters: letters, numbers, @, ., _, -
  const isValidChar = /^[a-zA-Z0-9@._-]$/.test(key);
  if (!isValidChar) {
    e.preventDefault();
    return;
  }

  // Prevent invalid starting characters
  if (value.length === 0 && /[@._-]/.test(key)) {
    e.preventDefault();
    return;
  }

  // Prevent double "@"
  if (key === "@" && value.includes("@")) {
    e.preventDefault();
    return;
  }

  // Prevent exceeding length
  const willExceed = maxLength !== undefined && value.length >= maxLength;
  if (willExceed) {
    e.preventDefault();
  }
};


// --- NEW FUNCTIONS FOR ZOD VALIDATION ---

// For allowOnlyNumbersWithLimit (mobile number needs to be exactly 10 digits, no leading zero)
export const validateMobileNumber = (val: string): boolean => {
    if (!val) return true; // Allow empty if it's not strictly required or handled by minLength
    return /^[1-9]\d{9}$/.test(val); // Starts with 1-9, followed by 9 digits
};

// For digitsIncludingZero (used for postal code, should be exactly 6 digits)
export const validatePostalCode = (val: string): boolean => {
    if (!val) return true;
    return /^\d{6}$/.test(val);
};

// For allowOnlyLettersWithSpecialChars (First Name, Middle Name, Last Name)
export const validateLettersWithSpecialChars = (maxLength: number) => (val: string): boolean => {
    if (!val) return true;
    const validChars = /^[a-zA-Z' -]+$/;
    // Ensure it doesn't start with a space or apostrophe if not empty
    const startsWithValidChar = !val.startsWith(' ') && !val.startsWith("'");
    return validChars.test(val) && startsWithValidChar && val.length <= maxLength;
};

// For allowOnlyLetters (City, State, Country)
export const validateLetters = (maxLength?: number) => (val: string): boolean => {
    if (!val) return true;
    const validChars = /^[a-zA-Z ]+$/;
    // Ensure it doesn't start with a space
    const startsWithValidChar = !val.startsWith(' ');
    return validChars.test(val) && startsWithValidChar && (!maxLength || val.length <= maxLength);
};

// For allowOnlyCoverageAmount (This seems less relevant for the current Doctor form fields, but good to have)
// For Zod, it's often better to parse and then validate the number if it's truly a number field.
// If it's just a string representation, we can validate the string format.
export const validateCoverageAmountString = (maxLength?: number) => (val: string): boolean => {
    if (!val) return true;
    // Allows digits and commas. Might need more specific regex for complex cases.
    const isValidFormat = /^[0-9,]+$/.test(val);
    // Prevent leading zero unless followed by a comma (e.g., "0,100")
    const leadingZeroCheck = !(val.length > 1 && val.startsWith('0') && val[1] !== ',');
    return isValidFormat && leadingZeroCheck && (!maxLength || val.length <= maxLength);
};

// For allowAlphaNumericWithPolicyChars (License Number)
export const validateAlphaNumericWithPolicyChars = (maxLength: number) => (val: string): boolean => {
    if (!val) return true;
    // Allows letters, digits, and policy-related special characters: /, -, .
    const isValidChar = /^[a-zA-Z0-9/\-. ]+$/; // Added space as it might be in policy docs
    // First character must be alphanumeric
    const isFirstCharValid = /^[a-zA-Z0-9]$/.test(val[0] || ''); // Safely check first char
    return isValidChar.test(val) && isFirstCharValid && val.length <= maxLength;
};

// For allowEmailCharactersOnly
export const validateEmailCharactersOnly = (maxLength: number) => (val: string): boolean => {
    if (!val) return true;
    // Allow typical email characters: letters, numbers, @, ., _, -
    const isValidChar = /^[a-zA-Z0-9@._-]+$/;
    if (!isValidChar.test(val)) return false;

    // Prevent invalid starting characters
    if (val.length > 0 && /[@._-]/.test(val[0])) return false;

    // Prevent double "@"
    if (val.split('@').length > 2) return false;

    return val.length <= maxLength;
};