import React from "react";

type Props = {
  panNumber: string;
  setPanNumber: (value: string) => void;
};

const PanInput: React.FC<Props> = ({ panNumber, setPanNumber }) => {
  return (
    <input
      type="text"
      maxLength={10}
      value={panNumber}
      onChange={(e) => setPanNumber(e.target.value.toUpperCase())}
      placeholder="Enter Driving License Number"
      className="flex-1 h-10 border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed"
    />
  );
};

export default PanInput;
