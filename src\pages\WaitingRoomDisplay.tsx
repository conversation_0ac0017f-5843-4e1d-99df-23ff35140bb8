import React, { useState, useEffect } from 'react';
import { 
  Monitor, 
  Clock, 
  Users, 
  Activity,
  Calendar,
  MapPin,
  Timer,
  Bell
} from 'lucide-react';
import { getAllQueues } from '../services/queueApis';
import { getDepartmentQueues, getTodayAvailableDoctors } from '../services/departmentApis';
import { ServiceType, QueueStatus } from '../types/queue';
import type { QueueSummary } from '../types/queue';
import type { DepartmentQueue, DepartmentAvailableDoctors } from '../types/department';
import { TodayDoctorAvailability } from '../components/waiting-room/TodayDoctorAvailability';
import { MinimalistWaitingRoom } from '../components/waiting-room/MinimalistWaitingRoom';

interface WaitingRoomDisplayProps {
  facilityId: string;
  displayMode?: 'full' | 'compact' | 'department' | 'minimalist';
}

const WaitingRoomDisplay: React.FC<WaitingRoomDisplayProps> = ({
  facilityId,
  displayMode = 'full'
}) => {
  const [queues, setQueues] = useState<{ [key in ServiceType]?: QueueSummary }>({});
  const [departmentQueues, setDepartmentQueues] = useState<DepartmentQueue[]>([]);
  const [todayAvailableDoctors, setTodayAvailableDoctors] = useState<DepartmentAvailableDoctors[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    if (displayMode === 'department') {
      loadDepartmentQueues();
      const queueInterval = setInterval(loadDepartmentQueues, 15000);

      const timeInterval = setInterval(() => {
        setCurrentTime(new Date());
      }, 1000);

      return () => {
        clearInterval(timeInterval);
        clearInterval(queueInterval);
      };
    } else {
      loadAllQueues();

      // Update time every second
      const timeInterval = setInterval(() => {
        setCurrentTime(new Date());
      }, 1000);

      // Refresh queue data every 15 seconds
      const queueInterval = setInterval(loadAllQueues, 15000);

      return () => {
        clearInterval(timeInterval);
        clearInterval(queueInterval);
      };
    }
  }, [facilityId, displayMode]);

  const loadAllQueues = async () => {
    setLoading(true);
    try {
      const data = await getAllQueues(facilityId);
      setQueues(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load queue data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDepartmentQueues = async () => {
    setLoading(true);
    try {
      const [queueResponse, doctorsResponse] = await Promise.all([
        getDepartmentQueues(facilityId),
        getTodayAvailableDoctors(facilityId)
      ]);

      if (queueResponse.success) {
        setDepartmentQueues(queueResponse.data);
      }

      if (doctorsResponse.success) {
        setTodayAvailableDoctors(doctorsResponse.data);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load department queue data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getServiceTypeDisplayName = (serviceType: ServiceType) => {
    const names = {
      [ServiceType.Consultation]: 'General Consultation',
      [ServiceType.Emergency]: 'Emergency',
      [ServiceType.Pharmacy]: 'Pharmacy',
      [ServiceType.Laboratory]: 'Laboratory',
      [ServiceType.Radiology]: 'Radiology',
      [ServiceType.Cardiology]: 'Cardiology',
      [ServiceType.Orthopedics]: 'Orthopedics',
      [ServiceType.Pediatrics]: 'Pediatrics',
      [ServiceType.Gynecology]: 'Gynecology',
      [ServiceType.Dermatology]: 'Dermatology'
    };
    return names[serviceType] || serviceType;
  };

  const getServiceIcon = (serviceType: ServiceType) => {
    const icons = {
      [ServiceType.Consultation]: Users,
      [ServiceType.Emergency]: Activity,
      [ServiceType.Pharmacy]: MapPin,
      [ServiceType.Laboratory]: Timer,
      [ServiceType.Radiology]: Monitor,
      [ServiceType.Cardiology]: Activity,
      [ServiceType.Orthopedics]: Users,
      [ServiceType.Pediatrics]: Users,
      [ServiceType.Gynecology]: Users,
      [ServiceType.Dermatology]: Users
    };
    return icons[serviceType] || Users;
  };

  const activeQueues = Object.entries(queues).filter(([_, queue]) =>
    queue && queue.totalInQueue > 0
  );

  // Minimalist display mode - Simple doctor availability
  if (displayMode === 'minimalist') {
    return <MinimalistWaitingRoom />;
  }

  // Department-based display mode
  if (displayMode === 'department') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-800 to-blue-900 text-white">
        {/* Header */}
        <div className="bg-white/10 backdrop-blur-sm border-b border-white/20 p-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Department-wise Queue Status</h1>
              <p className="text-blue-200">Real-time Provider & Department Queue Management</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{formatTime(currentTime)}</div>
              <div className="text-blue-200">{formatDate(currentTime)}</div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto p-6">
          {/* Today's Available Doctors Summary */}
          <TodayDoctorAvailability
            departments={todayAvailableDoctors}
            displayMode="full"
            className="mb-8"
          />

          {/* Department Queue Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {departmentQueues.map((deptQueue) => (
              <div
                key={deptQueue.departmentId}
                className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden"
              >
                {/* Department Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold">{deptQueue.departmentName}</h3>
                      <p className="text-blue-200 text-sm">{deptQueue.departmentCode}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-yellow-300">
                        {deptQueue.totalInQueue}
                      </div>
                      <div className="text-xs text-blue-200">Total in Queue</div>
                    </div>
                  </div>
                </div>

                {/* Currently Serving */}
                {deptQueue.currentlyServing && (
                  <div className="bg-green-500/20 border-b border-white/10 p-4">
                    <div className="flex items-center space-x-3">
                      <div className="bg-green-500 p-2 rounded-full">
                        <Users size={16} />
                      </div>
                      <div>
                        <div className="text-sm text-green-300">Now Serving</div>
                        <div className="font-semibold">
                          #{deptQueue.currentlyServing.queueNumber} - {deptQueue.currentlyServing.patientName}
                        </div>
                        <div className="text-xs text-green-200">
                          with {deptQueue.currentlyServing.providerName}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Provider Queues */}
                <div className="p-4 space-y-3">
                  {deptQueue.providerQueues.length > 0 ? (
                    deptQueue.providerQueues.map((providerQueue) => (
                      <div
                        key={providerQueue.providerId}
                        className={`rounded-lg p-3 border transition-all ${
                          providerQueue.isAvailable
                            ? 'bg-white/10 border-white/20'
                            : 'bg-gray-500/10 border-gray-500/20'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <div className="font-medium text-sm text-white">
                                {providerQueue.providerName}
                              </div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                providerQueue.status === 'AVAILABLE'
                                  ? 'bg-green-500/20 text-green-300'
                                  : providerQueue.status === 'BUSY'
                                  ? 'bg-yellow-500/20 text-yellow-300'
                                  : 'bg-red-500/20 text-red-300'
                              }`}>
                                {providerQueue.status}
                              </span>
                            </div>
                            <div className="text-xs text-gray-300 mb-1">
                              {providerQueue.specialization}
                            </div>
                            {!providerQueue.isAvailable && (
                              <div className="text-xs text-red-300">
                                Not available today
                              </div>
                            )}
                          </div>
                          <div className="text-right">
                            <div className={`text-lg font-bold ${
                              providerQueue.queueLength > 0 ? 'text-blue-300' : 'text-gray-400'
                            }`}>
                              {providerQueue.queueLength}
                            </div>
                            <div className="text-xs text-gray-400">
                              {providerQueue.queueLength === 1 ? 'patient' : 'patients'}
                            </div>
                          </div>
                        </div>

                        {/* Current Patient */}
                        {providerQueue.currentPatient && (
                          <div className="bg-green-500/20 rounded p-2 mb-2">
                            <div className="text-xs text-green-300 font-medium">
                              🟢 Currently Serving:
                            </div>
                            <div className="text-xs text-white">
                              #{providerQueue.currentPatient.queueNumber} - {providerQueue.currentPatient.patientName}
                            </div>
                          </div>
                        )}

                        {/* Next Patients */}
                        {providerQueue.nextPatients.length > 0 && (
                          <div className="mb-2">
                            <div className="text-xs text-gray-400 mb-1">Next in line:</div>
                            <div className="flex flex-wrap gap-1">
                              {providerQueue.nextPatients.slice(0, 4).map((patient, index) => (
                                <span
                                  key={patient.queueNumber}
                                  className={`inline-flex px-2 py-1 text-xs rounded ${
                                    index === 0
                                      ? 'bg-orange-500/20 text-orange-300 font-medium'
                                      : 'bg-blue-500/20 text-blue-300'
                                  }`}
                                >
                                  #{patient.queueNumber}
                                </span>
                              ))}
                              {providerQueue.nextPatients.length > 4 && (
                                <span className="inline-flex px-2 py-1 text-xs bg-gray-500/20 text-gray-300 rounded">
                                  +{providerQueue.nextPatients.length - 4} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Provider Stats */}
                        {providerQueue.isAvailable && (
                          <div className="flex items-center justify-between pt-2 border-t border-white/10">
                            <div className="text-xs text-gray-400">
                              <span>Avg: {providerQueue.averageServiceTime}min</span>
                            </div>
                            <div className="text-xs text-gray-400">
                              <span>Wait: {Math.ceil(providerQueue.estimatedWaitTime)}min</span>
                            </div>
                            <div className="text-xs text-blue-300">
                              <span>{Math.floor(Math.random() * 15) + 5} seen today</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-400">
                      <Users className="mx-auto h-8 w-8 mb-2" />
                      <p className="text-sm">No providers scheduled today</p>
                    </div>
                  )}
                </div>

                {/* Department Stats */}
                <div className="bg-white/5 border-t border-white/10 p-3">
                  <div className="flex items-center justify-between text-xs text-gray-300">
                    <span>Avg. Wait: {deptQueue.averageWaitTime}min</span>
                    <span>Est. Wait: {deptQueue.estimatedWaitTime}min</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {departmentQueues.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-16 w-16 text-white/50 mb-4" />
              <p className="text-xl text-white/80">No department queues available</p>
            </div>
          )}

          {/* Footer */}
          <div className="mt-8 text-center">
            <div className="bg-white/5 rounded-lg p-4 inline-block">
              <div className="flex items-center space-x-4 text-sm text-white/70">
                <div className="flex items-center space-x-2">
                  <Activity size={16} />
                  <span>Live Updates</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock size={16} />
                  <span>
                    Last updated: {lastUpdated ? formatTime(lastUpdated) : 'Never'}
                  </span>
                </div>
                {loading && (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Updating...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (displayMode === 'compact') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 to-blue-700 text-white p-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-2">OPD Queue Status</h1>
            <div className="text-xl opacity-90">
              {formatDate(currentTime)} | {formatTime(currentTime)}
            </div>

            {/* Today's Doctor Summary */}
            {todayAvailableDoctors.length > 0 && (
              <TodayDoctorAvailability
                departments={todayAvailableDoctors}
                displayMode="compact"
                className="mt-6"
              />
            )}
          </div>

          {/* Queue Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {activeQueues.map(([serviceType, queue]) => {
              const Icon = getServiceIcon(serviceType as ServiceType);
              const currentToken = queue?.currentlyServing?.queueEntry?.queueNumber;
              
              return (
                <div key={serviceType} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <div className="flex items-center space-x-3 mb-4">
                    <Icon className="text-white" size={24} />
                    <h3 className="text-lg font-semibold">
                      {getServiceTypeDisplayName(serviceType as ServiceType)}
                    </h3>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Current Token:</span>
                      <span className="text-2xl font-bold text-yellow-300">
                        {currentToken ? `#${currentToken}` : 'None'}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Waiting:</span>
                      <span className="text-xl font-semibold">
                        {queue?.totalInQueue || 0}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {activeQueues.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-16 w-16 text-white/50 mb-4" />
              <p className="text-xl text-white/80">No active queues at the moment</p>
            </div>
          )}

          {/* Footer */}
          <div className="text-center mt-8 text-white/60">
            <p>Last updated: {lastUpdated ? formatTime(lastUpdated) : 'Never'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-1">Hospital Queue Management System</h1>
            <p className="text-blue-200">Real-time OPD Queue Status</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{formatTime(currentTime)}</div>
            <div className="text-blue-200">{formatDate(currentTime)}</div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* Current Status Banner */}
        <div className="bg-gradient-to-r from-green-500 to-blue-500 rounded-lg p-6 mb-8 text-center">
          <div className="flex items-center justify-center space-x-4">
            <Bell className="text-white" size={32} />
            <div>
              <h2 className="text-2xl font-bold mb-1">Now Serving</h2>
              <div className="flex items-center space-x-6">
                {activeQueues.slice(0, 3).map(([serviceType, queue]) => {
                  const currentToken = queue?.currentlyServing?.queueEntry?.queueNumber;
                  return currentToken ? (
                    <div key={serviceType} className="text-center">
                      <div className="text-sm opacity-90">
                        {getServiceTypeDisplayName(serviceType as ServiceType)}
                      </div>
                      <div className="text-3xl font-bold text-yellow-300">
                        #{currentToken}
                      </div>
                    </div>
                  ) : null;
                })}
                {activeQueues.filter(([_, queue]) => queue?.currentlyServing).length === 0 && (
                  <div className="text-xl opacity-90">No patients currently being served</div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Today's Doctor Availability Summary */}
        {todayAvailableDoctors.length > 0 && (
          <TodayDoctorAvailability
            departments={todayAvailableDoctors}
            displayMode="summary"
            className="mb-8"
          />
        )}

        {/* Department Queue Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Object.entries(ServiceType).map(([key, serviceType]) => {
            const queue = queues[serviceType];
            const Icon = getServiceIcon(serviceType);
            const isActive = queue && queue.totalInQueue > 0;
            const currentToken = queue?.currentlyServing?.queueEntry?.queueNumber;
            const waitingCount = queue?.totalInQueue || 0;
            const nextTokens = queue?.queue
              ?.filter(p => p.status === QueueStatus.Waiting)
              ?.slice(0, 3)
              ?.map(p => p.queueNumber) || [];

            return (
              <div
                key={serviceType}
                className={`rounded-lg p-6 border transition-all duration-300 ${
                  isActive
                    ? 'bg-white/15 border-white/30 shadow-lg'
                    : 'bg-white/5 border-white/10'
                }`}
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`p-2 rounded-lg ${
                    isActive ? 'bg-blue-500' : 'bg-gray-600'
                  }`}>
                    <Icon size={24} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {getServiceTypeDisplayName(serviceType)}
                    </h3>
                    <p className={`text-sm ${
                      isActive ? 'text-green-300' : 'text-gray-400'
                    }`}>
                      {isActive ? 'Active' : 'No Queue'}
                    </p>
                  </div>
                </div>

                {isActive ? (
                  <div className="space-y-4">
                    {/* Current Token */}
                    <div className="bg-white/10 rounded-lg p-4 text-center">
                      <div className="text-sm text-blue-200 mb-1">Current Token</div>
                      <div className="text-3xl font-bold text-yellow-300">
                        {currentToken ? `#${currentToken}` : 'None'}
                      </div>
                    </div>

                    {/* Waiting Count */}
                    <div className="flex justify-between items-center">
                      <span className="text-white/80">Patients Waiting:</span>
                      <span className="text-xl font-bold text-blue-300">
                        {waitingCount}
                      </span>
                    </div>

                    {/* Next Tokens */}
                    {nextTokens.length > 0 && (
                      <div>
                        <div className="text-sm text-white/60 mb-2">Next in line:</div>
                        <div className="flex space-x-2">
                          {nextTokens.map((token, index) => (
                            <div
                              key={token}
                              className={`px-3 py-1 rounded text-sm font-medium ${
                                index === 0
                                  ? 'bg-orange-500 text-white'
                                  : 'bg-white/20 text-white/80'
                              }`}
                            >
                              #{token}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Provider Information */}
                    {queue?.currentlyServing?.queueEntry?.provider && (
                      <div className="bg-white/10 rounded p-2 mb-2">
                        <div className="text-xs text-blue-200 mb-1">Current Provider:</div>
                        <div className="text-sm font-medium text-white">
                          {queue.currentlyServing.queueEntry.provider.title} {queue.currentlyServing.queueEntry.provider.firstName} {queue.currentlyServing.queueEntry.provider.lastName}
                        </div>
                        <div className="text-xs text-blue-300">
                          {queue.currentlyServing.queueEntry.provider.specialization}
                        </div>
                      </div>
                    )}

                    {/* Average Wait Time */}
                    {queue?.averageServiceTime && (
                      <div className="flex items-center space-x-2 text-sm text-white/70">
                        <Timer size={16} />
                        <span>Avg. wait: {queue.averageServiceTime}min</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="mx-auto h-8 w-8 text-gray-500 mb-2" />
                    <p className="text-gray-400 text-sm">No patients in queue</p>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className="bg-white/5 rounded-lg p-4 inline-block">
            <div className="flex items-center space-x-4 text-sm text-white/70">
              <div className="flex items-center space-x-2">
                <Activity size={16} />
                <span>Live Updates</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock size={16} />
                <span>
                  Last updated: {lastUpdated ? formatTime(lastUpdated) : 'Never'}
                </span>
              </div>
              {loading && (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Updating...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WaitingRoomDisplay;
