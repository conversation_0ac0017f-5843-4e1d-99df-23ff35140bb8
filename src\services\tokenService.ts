import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_TOKEN_URL;

type RegisterTokenRequest = {
  patient_id: string;
  name: string;
  department_id: string;
  department_name: string;
  date_time: string;
};

type RegisterTokenResponse = {
  message: string;
  token: string;
  verify_url: string;
  qr_card_base64: string;
  daily_id: number;
  patient: {
    patient_id: string;
    department_id: string;
    datetime: string;
  };
};

export const registerToken = async (
  data: RegisterTokenRequest
): Promise<RegisterTokenResponse | null> => {
  try {
    const response = await axios.post(`${BASE_URL}/register`, data);
    return response.data;
  } catch (error: any) {
    console.error("Token registration error:", error.response?.data || error.message);
    return null;
  }
};
