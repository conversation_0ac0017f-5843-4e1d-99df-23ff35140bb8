import { forwardRef } from "react";
import type { InputHTMLAttributes, ChangeEventHandler } from "react";


function getTodayDateString() {
  const today = new Date();
  const year = today.getFullYear();
  const month = (today.getMonth() + 1).toString().padStart(2, "0");
  const day = today.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
}

type Props = InputHTMLAttributes<HTMLInputElement> & {
  onChange?: ChangeEventHandler<HTMLInputElement>;
  maxDate?: string;
};

export const Calendar = forwardRef<HTMLInputElement, Props>(
  ({ className = "", onChange,maxDate, ...props }, ref) => {

    const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
      const val = e.target.value; // "yyyy-mm-dd"
      if (val) {
        const parts = val.split("-");
        if (parts.length === 3) {
          const year = parts[0];
          if (year.length > 4) {
            // Trim year to 4 digits
            parts[0] = year.slice(0, 4);
            e.target.value = parts.join("-");
          }
        }
      }
      if (onChange) onChange(e);
    };

    return (
      <input
        ref={ref}
        type="date"
        max={maxDate}
        {...props}
        onChange={handleChange}
        className={`w-full px-4 py-2 text-sm rounded-lg border border-gray-300 bg-white shadow-sm 
          focus:ring-2 focus:ring-blue-500 focus:border-transparent 
          transition-all duration-200 ${className}`}
      />
    );
  }
);

Calendar.displayName = "Calendar";

// import { forwardRef } from "react";
// import type { InputHTMLAttributes, ChangeEventHandler } from "react";

// type Props = InputHTMLAttributes<HTMLInputElement> & {
//   onChange?: ChangeEventHandler<HTMLInputElement>;
// };

// const getMaxDate = () => {
//   const year = new Date().getFullYear() + 100;
//   return `${year}-12-31`;
// };

// export const Calendar = forwardRef<HTMLInputElement, Props>(
//   ({ className = "", onChange, min, max, ...props }, ref) => {

//     const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
//       const val = e.target.value; // "yyyy-mm-dd"
//       if (val) {
//         const parts = val.split("-");
//         if (parts.length === 3) {
//           const year = parts[0];
//           if (year.length > 4) {
//             parts[0] = year.slice(0, 4);
//             e.target.value = parts.join("-");
//           }
//         }
//       }
//       if (onChange) onChange(e);
//     };

//     return (
//       <input
//         ref={ref}
//         type="date"
//         min={min || "1900-01-01"}  // default min
//         max={max || getMaxDate()}  // default max = current year + 100
//         {...props}
//         onChange={handleChange}
//         className={`w-full px-4 py-2 text-sm rounded-lg border border-gray-300 bg-white shadow-sm 
//           focus:ring-2 focus:ring-blue-500 focus:border-transparent 
//           transition-all duration-200 ${className}`}
//       />
//     );
//   }
// );

// Calendar.displayName = "Calendar";
