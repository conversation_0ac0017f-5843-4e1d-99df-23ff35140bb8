// Updated imports
import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON>a<PERSON><PERSON>ch, FaUserMd, FaTrash, FaEye, FaEdit, FaCalendarAlt, FaEllipsisV, FaCog, FaBan
} from "react-icons/fa";
import { useNavigate, useLocation } from "react-router-dom";
import { getAllDoctors, deleteDoctor } from "../../services/doctorApis";
import DoctorProfileModal from "./DoctorProfileModal";
import ConfirmDialog from "../../utils/ConfirmDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getScheduleConfigs } from "../../services/scheduleApis";
import { deleteConfigsByConsultantId } from "../../services/scheduleApis"; // adjust import path
import { deleteConfigsByConsultant } from "../../services/consultantslot-config";



interface Doctor {
  staffId: string;
  fullName: string;
  roleType: string;
  specializations: string[];
  gender: string;
  dateOfBirth: string;
  mobileNumber: string;
  email: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
   primarySpecialization:string
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
   
  };
  createdAt: string;
}


const DoctorListPage = () => {
  const location = useLocation();
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string | null>(null);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const toastMessage = location.state?.toast;
    const toastType = location.state?.toastType || "success"; // default to success

    if (toastMessage) {
      if (toastType === "error") {
        toast.error(toastMessage);
      } else {
        toast.success(toastMessage);
      }

      // Clear state to prevent repeated toasts on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [doctorsWithSchedule, setDoctorsWithSchedule] = useState<Record<string, boolean>>({});
  const [confirmDelete, setConfirmDelete] = useState<null | string>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  


  useEffect(() => {
    const fetchData = async () => {
      try {
        const docRes = await getAllDoctors();
        if (!docRes.success) throw new Error("Failed to load Practitioner");

        const transformedDoctors: Doctor[] = docRes.data.map((doc: any): Doctor => {
  const name = [doc.firstName, doc.middleName, doc.lastName].filter(Boolean).join(" ");
  const fullName = doc.roleType === "Doctor" ? `Dr. ${name}` : name;
  return {
    ...doc,
    fullName,
    staffId: doc.staffId, // ✅ use staffId
  };
});

        transformedDoctors.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setDoctors(transformedDoctors);

      const scheduleResults = await Promise.all(
  transformedDoctors.map(async (doc) => {
    try {
      const res = await getScheduleConfigs({ consultantId: doc.staffId });
      const schedules = Array.isArray(res.results) ? res.results : [];
      const hasSchedule = schedules.length > 0;
      return { staffId: doc.staffId, hasSchedule };
    } catch (e) {
      console.error(`Schedule check failed for doctor ${doc.staffId}`, e);
      return { doctorId: doc.staffId, hasSchedule: false };
    }
  })
);


        const scheduleMap: Record<string, boolean> = {};
        scheduleResults.forEach(({ staffId, hasSchedule }) => {
          scheduleMap[staffId] = hasSchedule;
        });

        setDoctorsWithSchedule(scheduleMap);
        // console.log("✅ doctorsWithSchedule map:", scheduleMap);

      } catch (err) {
        console.error(err);
        toast.error("Unexpected error occurred.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      if (
        openDropdown &&
        !document.getElementById(`dropdown-btn-${openDropdown}`)?.contains(target) &&
        !document.getElementById(`dropdown-menu-${openDropdown}`)?.contains(target)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdown]);


  const filteredDoctors = doctors.filter(doc =>
    doc.fullName.toLowerCase().includes(search.toLowerCase()) ||
    doc.registrationNumber.toLowerCase().includes(search.toLowerCase())
  );

  const handleDelete = async () => {
    if (!confirmDeleteId) return;
    const res = await deleteDoctor(confirmDeleteId);
    if (res.success) {
      setDoctors(prev => prev.filter(d => d.staffId
!== confirmDeleteId));
      toast.success("Practitioner deactiavted.");
    } else {
      // toast.error("Delete failed.");
    }
    setConfirmDeleteId(null);
  };
  const handleConfirmDelete = async () => {
    if (!confirmDelete) return;

    setIsDeleting(true);
    const result = await deleteConfigsByConsultantId(confirmDelete);
    setIsDeleting(false);

    if (result.success) {
      toast.success("Schedule deleted successfully");
    } else if (result.status === 404) {
      toast.info("No schedule found for this practitioner.");
    } else {
      toast.error(result.error || "Failed to delete schedule");
    }

    setConfirmDelete(null);
  };

 return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 font-sans">
      <ToastContainer />
      <div className="max-w-7xl mx-auto px-1 py-8">
        {/* Header and Buttons */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-pink-500">
              User Management
            </h1>
            <p className="text-gray-600 text-lg">
              Manage Hospital Users and Practitioner schedules
            </p>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => navigate("/doctors/add")}
              className="bg-gradient-to-r from-indigo-600 to-pink-500 text-white px-6 py-3 rounded-xl hover:scale-105 transition-all shadow-lg"
            >
              <FaUserMd className="inline mr-2" /> Add User
            </button>
            <button
              onClick={() => navigate("/schedules")}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:scale-105 transition-all shadow-lg"
            >
              <FaCalendarAlt className="inline mr-2" /> Manage Schedules
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl shadow p-4 mb-6">
          <div className="relative">
            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              className="w-full pl-12 pr-4 py-3 text-base border border-gray-300 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-200"
              placeholder="Search by name or license number..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
        </div>

        {/* Table */}
        {loading ? (
          <div className="text-center py-20 text-gray-500">Loading Practitioner...</div>
        ) : (
          <div className="relative rounded-2xl shadow-lg border border-gray-200 bg-white overflow-visible">
            <table className="w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {["Name", "Role", "Specialization", "License Number", "Gender", "Phone",  "Actions"].map(h => (
                    <th key={h} className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wide">{h}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredDoctors.length === 0 ? (
                  <tr><td colSpan={7} className="text-center py-12 text-gray-500">No Practitioner found.</td></tr>
                ) : (
                  filteredDoctors.map((doc) => (
                    <tr key={doc.staffId} className="hover:bg-blue-50 transition">
                      <td className="px-8 py-4">
                        <div className="flex items-center gap-5">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-md">
                            {doc.fullName
                              .split(" ")
                              .map(n => n[0])
                              .slice(0, 2)
                              .join("")
                              .toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium text-gray-800 whitespace-nowrap overflow-hidden text-ellipsis">
                              {doc.fullName}
                            </div>
                            <div className="text-xs text-gray-500">{doc.roleType} · {doc.primarySpecialization}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.roleType}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc. primarySpecialization}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.registrationNumber}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.gender}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.mobileNumber}</td>
                    
                      <td className="px-4 py-4 text-sm text-gray-700">
                        <div className="flex items-center gap-2 justify-start w-full">
                          {/* Standard buttons - always present */}
                          <div className="flex items-center gap-2">
                            <button
                              className="bg-green-100 text-green-600 p-2 rounded-lg hover:bg-green-200 flex-shrink-0"
                              title="View Profile"
                              onClick={() => setSelectedDoctorId(doc.staffId)}
                            >
                              <FaEye />
                            </button>
                            <button
                              className="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200 flex-shrink-0"
                              title="Edit"
                              onClick={() => navigate(`/doctors/edit/${doc.staffId}`)}
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200 flex-shrink-0"
                              title="Delete"
                              onClick={() => setConfirmDeleteId(doc.staffId)}
                            >
                              <FaTrash />
                            </button>
                          </div>

                          {/* Dropdown button - only for doctors, but always takes up space */}
                          {/* <div className="relative inline-block text-left ml-2 flex-shrink-0">
                            {doc.roleType === "Doctor" ? (
                              <button
                                id={`dropdown-btn-${doc.staffId}`}
                                onClick={() => setOpenDropdown(openDropdown === doc.staffId ? null : doc.staffId)}
                                className="p-2 rounded-full hover:bg-gray-100 transition"
                                title="More actions"
                              >
                                <FaEllipsisV />
                              </button>
                            ) : (
                              
                              <div className="p-2 w-8 h-8"></div>
                            )}

                            
{openDropdown === doc.staffId && (
  <div
    id={`dropdown-menu-${doc.staffId}`}
    className="absolute z-50 mt-2 w-56 origin-top-right rounded-xl border border-gray-200 bg-white shadow-xl right-0"
  >
    <ul className="py-1 text-sm text-gray-700">
      {doctorsWithSchedule[doc.staffId] === true ? (
        <>
          <li>
            <button
              onClick={() => {
                navigate(`/schedule/view/${doc.staffId}`);
                setOpenDropdown(null);
              }}
              className="flex items-center w-full px-4 py-2 hover:bg-gray-100"
            >
              <FaCalendarAlt className="mr-2 text-green-600" />
              View Schedule
            </button>
          </li>
          <li>
            <button
              onClick={() => {
                navigate(`/schedule/form?mode=edit&doctorId=${doc.staffId}`);
                setOpenDropdown(null);
              }}
              className="flex items-center w-full px-4 py-2 hover:bg-gray-100"
            >
              <FaEdit className="mr-2 text-blue-600" />
              Edit Schedule
            </button>
          </li>
          <li>
            <button
              onClick={async () => {
                try {
                  const success = await deleteConfigsByConsultant(doc.staffId);
                  if (success) {
                    toast.success("Schedule deleted successfully");
                   
                    setDoctorsWithSchedule(prev => ({
                      ...prev,
                      [doc.staffId]: false
                    }));
                  } else {
                    toast.error("Failed to delete schedule");
                  }
                } catch (err) {
                  console.error(err);
                  toast.error("An error occurred");
                }
                setOpenDropdown(null);
              }}
              className="flex items-center w-full px-4 py-2 hover:bg-gray-100 text-red-700"
            >
              <FaTrash className="mr-2" />
              Delete Schedule
            </button>
          </li>
        </>
      ) : (
        <>
          <li>
            <button
              onClick={() => {
                navigate(`/schedule/form?mode=create&doctorId=${doc.staffId}`);
                setOpenDropdown(null);
              }}
              className="flex items-center w-full px-4 py-2 hover:bg-gray-100 text-indigo-700"
            >
              <FaCog className="mr-2" />
              Create Schedule
            </button>
          </li>
          <li>
            <button
              disabled
              className="flex items-center w-full px-4 py-2 text-gray-400 cursor-not-allowed"
              title="No schedule to delete"
            >
              <FaTrash className="mr-2" />
              Delete Schedule
            </button>
          </li>
        </>
      )}
    </ul>
  </div>
)}



                          </div> */}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Modals */}
        {selectedDoctorId && (
          <DoctorProfileModal
            doctorId={selectedDoctorId}
            onClose={() => setSelectedDoctorId(null)}
          />
        )}
        <ConfirmDialog
          isOpen={!!confirmDeleteId}
          title="Deactivate Doctor"
          message="Are you sure you want to deactivate this Practitioner? This action cannot be undone."
          onConfirm={handleDelete}
          onCancel={() => setConfirmDeleteId(null)}
        />
        <ConfirmDialog
          isOpen={!!confirmDelete}
          title="Delete Schedule"
          message="Are you sure you want to delete this Doctor's schedule?"
          onConfirm={handleConfirmDelete}
          onCancel={() => setConfirmDelete(null)}
        />
      </div>
    </div>
  );

};

export default DoctorListPage;