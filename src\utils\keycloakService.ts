import axios from "axios";

const keycloakBaseUrl = import.meta.env.VITE_KEY_CLOACK_URL;
const REALM = import.meta.env.VITE_REALM;
const CLIENT_ID = import.meta.env.VITE_CLIENT_ID;
const CLIENT_SECRET = import.meta.env.VITE_CLIENT_SECRET;

export async function loginWithKeycloak(username: string, password: string) {
  const params = new URLSearchParams();
  params.append("grant_type", "password");
  params.append("client_id", CLIENT_ID);
  params.append("client_secret", CLIENT_SECRET);
  params.append("username", username);
  params.append("password", password);

  try {
   const response = await axios.post(
  `${keycloakBaseUrl}/auth/realms/${REALM}/protocol/openid-connect/token`,
    params,
    { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
  );
    return response.data; // contains access_token, refresh_token, etc.
  } catch (error: any) {
    throw new Error(
      error.response?.data?.error_description || "Login failed"
    );
  }
}
