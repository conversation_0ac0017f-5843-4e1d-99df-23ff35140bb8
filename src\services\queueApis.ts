import axios from "axios";
import type {
  QueueEntry,
  QueueSummary,
  QueueStatistics,
  WaitTimeEstimation,
  QueueFilters
} from '../types/queue';
import { ServiceType, QueueStatus } from '../types/queue';
import {
  mockQueueEntries,
  mockQueueSummary,
  mockQueueStatistics,
  mockWaitTimeEstimations,
  generateMockQueueEntries,
  generateComprehensiveMockData,
  mockDoctorQueueData
} from '../mockData/queueMockData';

const BASE_URL = "https://megha-dev.sirobilt.com";
const USE_MOCK_DATA = true; // Set to false to use real API

// Mock data storage (simulates database)
let mockQueueData = [...mockQueueEntries, ...generateMockQueueEntries(15)];

// Get service queue
export const getServiceQueue = async (serviceType: ServiceType, facilityId: string, date?: string): Promise<QueueSummary> => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Use comprehensive mock data for better testing
      const comprehensiveData = generateComprehensiveMockData(facilityId);
      const queueSummary = comprehensiveData[serviceType];

      if (!queueSummary) {
        // Return empty queue if service not found
        return {
          serviceType,
          facilityId,
          date: date || new Date().toISOString().split('T')[0],
          totalInQueue: 0,
          queue: [],
          averageServiceTime: 15,
          estimatedWaitTime: 0,
          peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
          lastUpdated: new Date().toISOString()
        };
      }

      return {
        ...queueSummary,
        date: date || queueSummary.date
      };
    } catch (error: any) {
      console.error("Failed to fetch service queue:", error);
      throw new Error("Failed to fetch service queue");
    }
  } else {
    try {
      const params = new URLSearchParams();
      params.append('facilityId', facilityId);
      if (date) params.append('date', date);

      const response = await axios.get(`${BASE_URL}/queue/${serviceType}?${params}`);
      return response.data;
    } catch (error: any) {
      console.error("Failed to fetch service queue:", error);
      throw new Error("Failed to fetch service queue");
    }
  }
};

// Add patient to queue
export const addToQueue = async (
  serviceType: ServiceType,
  appointmentId: string,
  facilityId: string,
  priority: string
) => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const existingEntries = mockQueueData.filter(entry => 
        entry.serviceType === serviceType && 
        entry.facilityId === facilityId &&
        entry.status !== QueueStatus.Completed
      );

      const newQueueNumber = Math.max(...existingEntries.map(e => e.queueNumber), 0) + 1;
      
      const newEntry: QueueEntry = {
        queueId: `q-${Date.now()}`,
        appointmentId,
        patientId: `pat-${Date.now()}`,
        patientName: "New Patient",
        queueNumber: newQueueNumber,
        serviceType,
        priority: priority as any,
        status: QueueStatus.Waiting,
        estimatedWaitTime: existingEntries.length * 15,
        estimatedServiceTime: "11:30",
        joinedAt: new Date().toISOString(),
        facilityId,
        patient: {
          firstName: "New",
          lastName: "Patient",
          mobileNumber: "+91-**********",
          age: 30,
          gender: "Male"
        }
      };

      mockQueueData.push(newEntry);
      return { success: true, data: newEntry };
    } catch (error: any) {
      console.error("Add to Queue Error:", error);
      return { success: false, error: "Failed to add to queue" };
    }
  } else {
    try {
      const response = await axios.post(`${BASE_URL}/queue/${serviceType}/add`, {
        appointmentId,
        facilityId,
        priority
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      console.error("Add to Queue Error:", error.response?.data || error.message);
      return { success: false, error: error.response?.data || error.message };
    }
  }
};

// Update queue status
export const updateQueueStatus = async (
  serviceType: ServiceType,
  queueId: string,
  status: QueueStatus,
  notes?: string
) => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const entryIndex = mockQueueData.findIndex(entry => entry.queueId === queueId);
      if (entryIndex === -1) {
        return { success: false, error: "Queue entry not found" };
      }

      const updatedEntry = {
        ...mockQueueData[entryIndex],
        status,
        notes,
        ...(status === QueueStatus.Called && { calledAt: new Date().toISOString() }),
        ...(status === QueueStatus.InService && { serviceStartedAt: new Date().toISOString() }),
        ...(status === QueueStatus.Completed && { serviceCompletedAt: new Date().toISOString() })
      };

      mockQueueData[entryIndex] = updatedEntry;
      return { success: true, data: updatedEntry };
    } catch (error: any) {
      console.error("Update Queue Status Error:", error);
      return { success: false, error: "Failed to update queue status" };
    }
  } else {
    try {
      const response = await axios.patch(`${BASE_URL}/queue/${serviceType}/${queueId}`, {
        status,
        notes
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      console.error("Update Queue Status Error:", error.response?.data || error.message);
      return { success: false, error: error.response?.data || error.message };
    }
  }
};

// Remove from queue
export const removeFromQueue = async (
  serviceType: ServiceType,
  queueId: string,
  reason: string
) => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const entryIndex = mockQueueData.findIndex(entry => entry.queueId === queueId);
      if (entryIndex === -1) {
        return { success: false, error: "Queue entry not found" };
      }

      mockQueueData.splice(entryIndex, 1);
      return { success: true, message: "Removed from queue successfully" };
    } catch (error: any) {
      console.error("Remove from Queue Error:", error);
      return { success: false, error: "Failed to remove from queue" };
    }
  } else {
    try {
      const response = await axios.delete(`${BASE_URL}/queue/${serviceType}/${queueId}`, {
        data: { reason }
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      console.error("Remove from Queue Error:", error.response?.data || error.message);
      return { success: false, error: error.response?.data || error.message };
    }
  }
};

// Get wait time estimation
export const getWaitTimeEstimation = async (
  serviceType: ServiceType,
  facilityId: string,
  priority?: string
): Promise<WaitTimeEstimation> => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const estimation = mockWaitTimeEstimations[serviceType];
      if (!estimation) {
        throw new Error("Wait time estimation not available for this service");
      }

      return {
        ...estimation,
        lastUpdated: new Date().toISOString()
      };
    } catch (error: any) {
      console.error("Failed to fetch wait time estimation:", error);
      throw new Error("Failed to fetch wait time estimation");
    }
  } else {
    try {
      const params = new URLSearchParams();
      params.append('facilityId', facilityId);
      if (priority) params.append('priority', priority);

      const response = await axios.get(`${BASE_URL}/queue/${serviceType}/wait-time?${params}`);
      return response.data;
    } catch (error: any) {
      console.error("Failed to fetch wait time estimation:", error);
      throw new Error("Failed to fetch wait time estimation");
    }
  }
};

// Get queue statistics
export const getQueueStatistics = async (
  facilityId: string,
  serviceType?: ServiceType,
  date?: string
): Promise<QueueStatistics> => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        ...mockQueueStatistics,
        facilityId,
        date: date || new Date().toISOString().split('T')[0]
      };
    } catch (error: any) {
      console.error("Failed to fetch queue statistics:", error);
      throw new Error("Failed to fetch queue statistics");
    }
  } else {
    try {
      const params = new URLSearchParams();
      params.append('facilityId', facilityId);
      if (serviceType) params.append('serviceType', serviceType);
      if (date) params.append('date', date);

      const response = await axios.get(`${BASE_URL}/queue/stats?${params}`);
      return response.data;
    } catch (error: any) {
      console.error("Failed to fetch queue statistics:", error);
      throw new Error("Failed to fetch queue statistics");
    }
  }
};

// Get all queues for facility
export const getAllQueues = async (facilityId: string): Promise<{ [key in ServiceType]?: QueueSummary }> => {
  if (USE_MOCK_DATA) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));

      // Use comprehensive mock data for better testing
      const comprehensiveData = generateComprehensiveMockData(facilityId);

      console.log("Generated comprehensive mock data:", comprehensiveData);

      return comprehensiveData;
    } catch (error: any) {
      console.error("Failed to fetch all queues:", error);
      throw new Error("Failed to fetch all queues");
    }
  } else {
    try {
      const response = await axios.get(`${BASE_URL}/queue/all?facilityId=${facilityId}`);
      return response.data;
    } catch (error: any) {
      console.error("Failed to fetch all queues:", error);
      throw new Error("Failed to fetch all queues");
    }
  }
};
