import axios from "axios";

const OTP_API_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Sends an OTP to the specified phone number.
 * @param phoneNumber - 10-digit mobile number (without +91)
 */
export const sendOtp = async (phoneNumber: string) => {
  try {
    const fullPhoneNumber = `+91${phoneNumber}`;
    const response = await axios.post(`${OTP_API_URL}/api/mobile/verify/send`, {
      phoneNumber: fullPhoneNumber,
    });

    if (response.data?.status === "pending") {
      return {
        success: true,
        data: response.data,
        message: "OTP sent successfully",
      };
    } else {
      return {
        success: false,
        message: "Unable to send OTP. Please try again.",
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.error || "Failed to send OTP",
      status: error?.response?.status,
    };
  }
};

/**
 * Verifies the OTP entered by the user.
 * @param phoneNumber - 10-digit mobile number (without +91)
 * @param code - OTP code entered by user
 */
export const verifyOtp = async (phoneNumber: string, code: string) => {
  try {
    const fullPhoneNumber = `+91${phoneNumber}`;
    const response = await axios.post(`${OTP_API_URL}/api/mobile/verify/check`, {
      phoneNumber: fullPhoneNumber,
      code,
    });

    if (response.data?.status === "approved") {
      return {
        success: true,
        data: response.data,
        message: "OTP verified successfully",
      };
    } else {
      return {
        success: false,
        message: "OTP verification failed",
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.error || "OTP verification failed",
      status: error?.response?.status,
    };
  }
};
