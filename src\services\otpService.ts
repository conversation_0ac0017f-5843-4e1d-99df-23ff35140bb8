import axios from "axios";

const OTP_API_URL = import.meta.env.VITE_OTP_API_URL;

export const sendOtp = async (phoneNumber: string) => {
  try {
    const fullPhoneNumber = `+91${phoneNumber}`;
    const response = await axios.post(`${OTP_API_URL}/api/otp/send`, {
      phoneNumber: fullPhoneNumber,
    });

    return {
      success: true,
      data: response.data,
      message: "OTP sent successfully",
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.message || "Failed to send OTP",
      status: error?.response?.status,
    };
  }
};

export const verifyOtp = async (phoneNumber: string, code: string) => {
  try {
    const fullPhoneNumber = `+91${phoneNumber}`;
    const response = await axios.post(`${OTP_API_URL}/api/otp/verify`, {
      phoneNumber: fullPhoneNumber,
      code,
    });

    return {
      success: true,
      data: response.data,
      message: "<PERSON><PERSON> verified successfully",
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.message || "OTP verification failed",
      status: error?.response?.status,
    };
  }
};
