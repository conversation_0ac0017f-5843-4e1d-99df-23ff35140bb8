import React, { useState, useEffect } from "react";
import { 
  Users, 
  Clock, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Phone,
  RefreshCw,
  Filter,
  BarChart3
} from "lucide-react";
import { useQueueStore } from "../../store/queueStore";
import { ServiceType, QueueStatus } from "../../types/queue";
import { AppointmentPriority } from "../../types/appointmentenums";
import { getServiceQueue, getAllQueues, getQueueStatistics } from "../../services/queueApis";
import { Button } from "../../commonfields/Button";
import { Select } from "../../commonfields/Select";
import { showError, showSuccess } from "../../utils/toastUtils";
import { QueueCard } from "./QueueCard";
import { WaitTimeDisplay } from "./WaitTimeDisplay";
import { QueueStatsDashboard } from "./QueueStatsDashboard";

interface QueueManagementProps {
  facilityId: string;
  showStatistics?: boolean;
}

export const QueueManagement: React.FC<QueueManagementProps> = ({
  facilityId,
  showStatistics = true
}) => {
  const [selectedService, setSelectedService] = useState<ServiceType>(ServiceType.Consultation);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  const [view, setView] = useState<'overview' | 'detailed' | 'statistics'>('overview');

  const {
    queues,
    currentQueue,
    queueStatistics,
    loading,
    setQueues,
    setCurrentQueue,
    setQueueStatistics,
    setLoading,
    setSelectedFacilityId,
    setSelectedServiceType
  } = useQueueStore();

  useEffect(() => {
    setSelectedFacilityId(facilityId);
    loadAllQueues();
    loadQueueStatistics();
  }, [facilityId]);

  useEffect(() => {
    if (selectedService) {
      setSelectedServiceType(selectedService);
      loadServiceQueue(selectedService);
    }
  }, [selectedService]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        if (view === 'overview') {
          loadAllQueues();
        } else if (view === 'detailed' && selectedService) {
          loadServiceQueue(selectedService);
        }
        if (showStatistics) {
          loadQueueStatistics();
        }
      }, 30000); // Refresh every 30 seconds

      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, view, selectedService]);

  const loadAllQueues = async () => {
    setLoading(true);
    try {
      const allQueues = await getAllQueues(facilityId);
      setQueues(allQueues);
    } catch (error) {
      console.error("Failed to load queues:", error);
      showError("Failed to load queue data");
    } finally {
      setLoading(false);
    }
  };

  const loadServiceQueue = async (serviceType: ServiceType) => {
    setLoading(true);
    try {
      const queue = await getServiceQueue(serviceType, facilityId);
      setCurrentQueue(queue);
    } catch (error) {
      console.error("Failed to load service queue:", error);
      showError("Failed to load service queue");
    } finally {
      setLoading(false);
    }
  };

  const loadQueueStatistics = async () => {
    try {
      const stats = await getQueueStatistics(facilityId);
      setQueueStatistics(stats);
    } catch (error) {
      console.error("Failed to load queue statistics:", error);
    }
  };

  const handleRefresh = () => {
    if (view === 'overview') {
      loadAllQueues();
    } else if (view === 'detailed' && selectedService) {
      loadServiceQueue(selectedService);
    }
    if (showStatistics) {
      loadQueueStatistics();
    }
    showSuccess("Queue data refreshed");
  };

  const getServiceIcon = (serviceType: ServiceType) => {
    switch (serviceType) {
      case ServiceType.Emergency:
        return <AlertCircle className="text-red-500" size={20} />;
      case ServiceType.Consultation:
        return <Users className="text-blue-500" size={20} />;
      case ServiceType.Laboratory:
        return <Activity className="text-green-500" size={20} />;
      case ServiceType.Pharmacy:
        return <CheckCircle className="text-purple-500" size={20} />;
      default:
        return <Clock className="text-gray-500" size={20} />;
    }
  };

  const getStatusColor = (status: QueueStatus) => {
    switch (status) {
      case QueueStatus.Waiting:
        return "bg-yellow-100 text-yellow-800";
      case QueueStatus.Called:
        return "bg-blue-100 text-blue-800";
      case QueueStatus.InService:
        return "bg-green-100 text-green-800";
      case QueueStatus.Completed:
        return "bg-gray-100 text-gray-800";
      case QueueStatus.NoShow:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: AppointmentPriority) => {
    switch (priority) {
      case AppointmentPriority.Emergency:
        return "bg-red-500 text-white";
      case AppointmentPriority.Urgent:
        return "bg-orange-500 text-white";
      case AppointmentPriority.High:
        return "bg-yellow-500 text-white";
      case AppointmentPriority.Normal:
        return "bg-blue-500 text-white";
      case AppointmentPriority.Low:
        return "bg-gray-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  if (loading && Object.keys(queues).length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading queue data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Queue Management</h2>
          <p className="text-gray-600">Real-time patient queue monitoring and management</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* View Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setView('overview')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                view === 'overview' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setView('detailed')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                view === 'detailed' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Detailed
            </button>
            {showStatistics && (
              <button
                onClick={() => setView('statistics')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  view === 'statistics' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BarChart3 size={16} className="inline mr-1" />
                Stats
              </button>
            )}
          </div>

          {/* Auto Refresh Toggle */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="form-checkbox h-4 w-4 text-indigo-600"
            />
            <span className="text-sm text-gray-600">Auto Refresh</span>
          </label>

          {/* Refresh Button */}
          <Button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            <RefreshCw size={16} className={loading ? "animate-spin" : ""} />
            <span>Refresh</span>
          </Button>
        </div>
      </div>

      {/* Statistics View */}
      {view === 'statistics' && showStatistics && queueStatistics && (
        <QueueStatsDashboard statistics={queueStatistics} />
      )}

      {/* Overview - All Services */}
      {view === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Object.values(ServiceType).map(serviceType => {
            const queue = queues[serviceType];
            return (
              <QueueCard
                key={serviceType}
                serviceType={serviceType}
                queue={queue}
                onViewDetails={() => {
                  setSelectedService(serviceType);
                  setView('detailed');
                }}
              />
            );
          })}
        </div>
      )}

      {/* Detailed View - Single Service */}
      {view === 'detailed' && (
        <div className="space-y-6">
          {/* Service Selector */}
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Service:</label>
            <Select
              value={selectedService}
              onChange={(e) => setSelectedService(e.target.value as ServiceType)}
              className="w-48"
            >
              {Object.values(ServiceType).map(service => (
                <option key={service} value={service}>
                  {service}
                </option>
              ))}
            </Select>
          </div>

          {/* Wait Time Display */}
          <WaitTimeDisplay 
            serviceType={selectedService} 
            facilityId={facilityId} 
          />

          {/* Current Queue */}
          {currentQueue && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  {getServiceIcon(selectedService)}
                  <h3 className="text-lg font-semibold text-gray-800">
                    {selectedService} Queue
                  </h3>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-medium">
                    {currentQueue.totalInQueue} in queue
                  </span>
                </div>
                
                <div className="text-sm text-gray-600">
                  Last updated: {new Date(currentQueue.lastUpdated).toLocaleTimeString()}
                </div>
              </div>

              {/* Currently Serving */}
              {currentQueue.currentlyServing && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-green-800 mb-2">Currently Serving</h4>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        #{currentQueue.currentlyServing.queueEntry.queueNumber} - {currentQueue.currentlyServing.queueEntry.patientName}
                      </p>
                      <p className="text-sm text-gray-600">
                        Started at: {currentQueue.currentlyServing.serviceStartTime}
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(currentQueue.currentlyServing.queueEntry.priority)}`}>
                      {currentQueue.currentlyServing.queueEntry.priority}
                    </span>
                  </div>
                </div>
              )}

              {/* Queue List */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-800">Waiting Queue</h4>
                {currentQueue.queue.filter(entry => entry.status === QueueStatus.Waiting || entry.status === QueueStatus.Called).length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="mx-auto text-gray-400 mb-4" size={48} />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No patients in queue</h3>
                    <p className="text-gray-500">The queue is currently empty</p>
                  </div>
                ) : (
                  currentQueue.queue
                    .filter(entry => entry.status === QueueStatus.Waiting || entry.status === QueueStatus.Called)
                    .map((entry) => (
                      <div
                        key={entry.queueId}
                        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="text-lg font-bold text-gray-900">
                            #{entry.queueNumber}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{entry.patientName}</p>
                            <p className="text-sm text-gray-600">
                              Wait time: ~{entry.estimatedWaitTime} min | ETA: {entry.estimatedServiceTime}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(entry.priority)}`}>
                            {entry.priority}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(entry.status)}`}>
                            {entry.status}
                          </span>
                          {entry.patient?.mobileNumber && (
                            <Button
                              onClick={() => window.open(`tel:${entry.patient?.mobileNumber}`)}
                              className="p-2 text-gray-400 hover:text-gray-600"
                            >
                              <Phone size={16} />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
