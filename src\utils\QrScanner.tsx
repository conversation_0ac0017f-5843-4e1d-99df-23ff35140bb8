import React, { useEffect, useRef, useState } from 'react';
import { Html5Qrcode, Html5QrcodeScannerState } from 'html5-qrcode';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaCamera, FaTimes } from 'react-icons/fa';
import { useRoles } from '../hooks/useRoles';

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

const QrScanner: React.FC = () => {
  const navigate = useNavigate();
  const { hasRole } = useRoles();

  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [scannerId] = useState('qr-reader-container');
  const [lastScannedId, setLastScannedId] = useState<string | null>(null);
  const [scanCooldown, setScanCooldown] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Scanner gun handling
  const [scanBuffer, setScanBuffer] = useState('');
  const scanInputRef = useRef<HTMLInputElement>(null);
  const scanBufferTimeout = useRef<number | null>(null);

  const stopScanner = async (): Promise<void> => {
    try {
      const scanner = scannerRef.current;
      if (scanner && isScanning) {
        try {
          const state = await scanner.getState();
          if (state === Html5QrcodeScannerState.SCANNING) {
            await scanner.stop();
          }
        } catch {
          // If getState fails, try to stop anyway
          try {
            await scanner.stop();
          } catch (stopError) {
            console.error('Force stop error:', stopError);
          }
        }

        try {
          await scanner.clear();
        } catch (clearError) {
          console.error('Clear error:', clearError);
        }

        setIsScanning(false);
        scannerRef.current = null;
      }
    } catch (err) {
      console.error('Scanner stop error:', err);
      setIsScanning(false);
    }
  };

 const stopAndNavigate = async (path: string): Promise<void> => {
  setIsProcessing(false);
  await stopScanner();

  // Wait for DOM updates before navigating
  requestAnimationFrame(() => {
    setTimeout(() => navigate(path, { replace: true }), 100);
  });
};


  const processPatientId = async (patientId: string): Promise<void> => {
    // Prevent duplicate processing
    if (isProcessing || scanCooldown || patientId === lastScannedId) {
      console.log('Duplicate scan ignored:', patientId);
      return;
    }

    setIsProcessing(true);
    setLastScannedId(patientId);
    setScanCooldown(true);

    // Reset cooldown after 3 seconds
    setTimeout(() => {
      setScanCooldown(false);
    }, 3000);

    try {
      // Make API call to fetch patient details
      const res = await fetch(
        `${apiBaseUrl}/api/patients?upId=${encodeURIComponent(patientId)}`
      );
      
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();

      if (Array.isArray(data) && data.length > 0) {
        toast.success('Patient found! Redirecting...');
        await stopAndNavigate(`/patient/${patientId}`);
      } else {
        toast.error('Patient not found for this QR Code');
        await stopAndNavigate('/list');
      }
    } catch (err) {
      console.error('API error:', err);
      toast.error('Network error. Please try again.');
      await stopAndNavigate('/list');
    }
  };

  const handleQrScanSuccess = async (decodedText: string): Promise<void> => {
    console.log('Scanned input:', decodedText);

    // Stop scanner immediately to prevent multiple scans
    if (scannerRef.current && isScanning) {
      try {
        await stopScanner();
      } catch (err) {
        console.error('Error stopping scanner after successful scan:', err);
      }
    }

    // Match any valid patient ID in format 001-00-0000-0002
    const match = decodedText.match(/([0-9]{3}-[0-9]{2}-[0-9]{4}-[0-9]{4})/);
    if (!match || !match[1]) {
      toast.error('Invalid QR Code Format. Expected format');
      await stopAndNavigate('/list');
      return;
    }

    const patientId = match[1];
    await processPatientId(patientId);
  };

  const handleQrScanError = (errorMessage: string): void => {
    // Only log actual errors, not the constant "No QR code found" messages
    if (!errorMessage.includes('No QR code found')) {
      console.debug('Scan error:', errorMessage);
    }
  };

  // Start camera scanner on mount
useEffect(() => {
  if (!(hasRole('doctor') || hasRole('admin'))) return;

  const initScanner = async () => {
    const container = document.getElementById(scannerId);
    if (!container) {
      console.error('QR container not found in DOM');
      return;
    }

    if (scannerRef.current) return; // avoid re-init

    container.innerHTML = '';

    const scanner = new Html5Qrcode(scannerId);
    scannerRef.current = scanner;

    const config = {
      fps: 10,
      qrbox: { width: 400, height: 400 },
      rememberLastUsedCamera: true,
      aspectRatio: 1.0,
    };

    try {
      const devices = await Html5Qrcode.getCameras();
      if (!devices || devices.length === 0) {
        toast.error('No camera found on this device.');
        return;
      }

      const backCamera = devices.find(d =>
        d.label.toLowerCase().includes('back') || d.label.toLowerCase().includes('rear')
      );
      const cameraId = backCamera?.id || devices[0].id;

      await scanner.start(cameraId, config, handleQrScanSuccess, handleQrScanError);
      setIsScanning(true);
      console.log('Camera scanner started successfully');
    } catch (err) {
      console.error('Scanner start error:', err);
      setIsScanning(false);
    }
  };

  const timeout = setTimeout(initScanner, 100);

  return () => {
    clearTimeout(timeout);
    stopScanner();
  };
}, [hasRole]);

  // Handle scanner gun input
// Handle scanner gun input and keep input focused
useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (isProcessing || (e.target as HTMLElement)?.tagName === 'INPUT') return;

    if (e.key === 'Enter') {
      if (scanBuffer.trim() !== '') {
        console.log('Scanner gun input:', scanBuffer.trim());
        handleQrScanSuccess(scanBuffer.trim());
        setScanBuffer('');
      }
    } else if (e.key.length === 1) {
      setScanBuffer(prev => prev + e.key);

      if (scanBufferTimeout.current) clearTimeout(scanBufferTimeout.current);
      scanBufferTimeout.current = window.setTimeout(() => setScanBuffer(''), 2000);
    }
  };

  const forceFocus = () => {
    if (scanInputRef.current) {
      scanInputRef.current.focus();
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  const interval = setInterval(forceFocus, 1000); // Keeps input focused

  return () => {
    document.removeEventListener('keydown', handleKeyDown);
    if (scanBufferTimeout.current) clearTimeout(scanBufferTimeout.current);
    clearInterval(interval);
  };
}, [scanBuffer, isProcessing]);


  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanner();
    };
  }, []);

  // Stop scanner when tab is hidden
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isScanning) {
        stopScanner();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isScanning]);

  if (!(hasRole('doctor') || hasRole('admin'))) {
    return <div className="p-6 text-center text-red-500">Access Denied</div>;
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/40 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl p-6 border border-gray-200">
        <button
          onClick={() => stopAndNavigate('/list')}
          className="absolute top-4 right-4 text-gray-500 hover:text-red-600 hover:scale-110 transition z-10"
          disabled={isProcessing}
        >
          <FaTimes className="w-6 h-6" />
        </button>

        <div className="mb-6 text-center">
          <h2 className="text-3xl font-bold text-gray-800">Scan Patient QR Code</h2>
          <p className="text-base text-gray-500 mt-2">Use your camera or scanner gun to scan the QR code</p>
          <FaCamera className="w-8 h-8 mt-3 text-indigo-600 mx-auto" />
        </div>

        {/* Camera scanner container - Much larger */}
        <div
          id={scannerId}
          className="w-full h-[500px] max-w-2xl mx-auto border-2 border-dashed border-gray-300 rounded-xl overflow-hidden bg-gray-50 flex items-center justify-center"
        />

        {/* Hidden input for maintaining focus for scanner guns */}
     <input
  ref={scanInputRef}
  type="text"
  style={{
    position: 'absolute',
    opacity: 0,
    height: 0,
    width: 0,
    pointerEvents: 'none'
  }}
  autoFocus
  tabIndex={-1}
/>

        {/* Status indicators */}
        <div className="mt-6 text-center">
          {isProcessing && (
            <div className="flex items-center justify-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
              <p className="text-base text-indigo-600 font-medium">Processing QR code...</p>
            </div>
          )}
          {isScanning && !isProcessing && (
            <div className="space-y-2">
              <p className="text-base text-green-600 font-medium">
                📱 Camera is active - Point QR code at the scanner area
              </p>
              <p className="text-sm text-gray-500">
                🔫 Scanner gun is also ready for input
              </p>
            </div>
          )}
          {!isScanning && !isProcessing && (
            <p className="text-base text-gray-500">Starting camera...</p>
          )}
        </div>

        {/* Debug info for scanner gun (only in development) */}
        {typeof window !== 'undefined' && window.location.hostname === 'localhost' && scanBuffer && (
          <div className="mt-3 text-center">
            <p className="text-sm text-gray-400">Scanner Buffer: {scanBuffer}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QrScanner;