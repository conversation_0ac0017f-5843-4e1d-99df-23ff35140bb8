import React, { useEffect, useRef, useState } from 'react';
import { Html5Qrcode, Html5QrcodeScannerState } from 'html5-qrcode';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaCamera, FaTimes } from 'react-icons/fa';
import { useRoles } from '../hooks/useRoles';

const QrScanner: React.FC = () => {
  const navigate = useNavigate();
  const { hasRole } = useRoles();

  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [scannerId] = useState('qr-reader-container');
  const [lastScannedId, setLastScannedId] = useState<string | null>(null);
  const [scanCooldown, setScanCooldown] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const [scanBuffer, setScanBuffer] = useState('');
  const scanInputRef = useRef<HTMLInputElement>(null);
  const scanBufferTimeout = useRef<number | null>(null);

  const stopScanner = async (): Promise<void> => {
    try {
      const scanner = scannerRef.current;
      if (scanner && isScanning) {
        try {
          const state = await scanner.getState();
          if (state === Html5QrcodeScannerState.SCANNING) {
            await scanner.stop();
          }
        } catch {
          try {
            await scanner.stop();
          } catch (stopError) {
            console.error('Force stop error:', stopError);
          }
        }

        try {
          await scanner.clear();
        } catch (clearError) {
          console.error('Clear error:', clearError);
        }

        setIsScanning(false);
        scannerRef.current = null;
      }
    } catch (err) {
      console.error('Scanner stop error:', err);
      setIsScanning(false);
    }
  };

  const stopAndNavigate = async (path: string): Promise<void> => {
    setIsProcessing(false);
    await stopScanner(); // Ensure scanner is stopped first
    requestAnimationFrame(() => {
      setTimeout(() => navigate(path, { replace: true }), 100);
    });
  };

  const processPatientId = async (patientId: string): Promise<void> => {
    if (isProcessing || scanCooldown || patientId === lastScannedId) {
      console.log('Duplicate scan ignored:', patientId);
      return;
    }

    setIsProcessing(true);
    setLastScannedId(patientId);
    setScanCooldown(true);

    setTimeout(() => {
      setScanCooldown(false);
    }, 3000);

    try {
 const res = await fetch(
  `${import.meta.env.VITE_ABHA_BASE_URL}/patients?upId=${encodeURIComponent(patientId)}`
);


      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();

      if (Array.isArray(data) && data.length > 0) {
        toast.success('Patient found! Redirecting...');
        await stopAndNavigate(`/patient/${patientId}`);
      } else {
        toast.error('Patient not found for this QR Code');
        await stopAndNavigate('/list');
      }
    } catch (err) {
      console.error('API error:', err);
      toast.error('Network error. Please try again.');
      await stopAndNavigate('/list');
    }
  };

const handleQrScanSuccess = async (decodedText: string): Promise<void> => {
  console.log('Scanned input:', decodedText);

  // Updated to extract patient ID from full URL like https://megha-dev.sirobilt.com/patient/00-0000-0045
  try {
    const url = new URL(decodedText);
    const pathSegments = url.pathname.split('/');
    const patientId = pathSegments[pathSegments.length - 1];

    if (!patientId || !/^(\d{2}-\d{4}-\d{4})$/.test(patientId)) {
      throw new Error('Invalid patient ID format');
    }

    await processPatientId(patientId);
  } catch (err) {
    console.error('Invalid QR Code:', err);
    toast.error('Invalid QR Code format. Expected a valid patient URL');
    await stopAndNavigate('/list');
  }
};



  const handleQrScanError = (errorMessage: string): void => {
    if (!errorMessage.includes('No QR code found')) {
      console.debug('Scan error:', errorMessage);
    }
  };

  useEffect(() => {
    if (!(hasRole('doctor') || hasRole('admin'))) return;

    const initScanner = async () => {
      const container = document.getElementById(scannerId);
      if (!container) {
        console.error('QR container not found in DOM');
        return;
      }

      if (scannerRef.current) return;

      container.innerHTML = '';

      const scanner = new Html5Qrcode(scannerId);
      scannerRef.current = scanner;

      const config = {
        fps: 10,
        qrbox: { width: 400, height: 400 },
        rememberLastUsedCamera: true,
        aspectRatio: 1.0,
      };

      try {
        const devices = await Html5Qrcode.getCameras();
        if (!devices || devices.length === 0) {
          toast.error('No camera found on this device.');
          return;
        }

        const backCamera = devices.find(d =>
          d.label.toLowerCase().includes('back') || d.label.toLowerCase().includes('rear')
        );
        const cameraId = backCamera?.id || devices[0].id;

        await scanner.start(cameraId, config, handleQrScanSuccess, handleQrScanError);
        setIsScanning(true);
        console.log('Camera scanner started successfully');
      } catch (err) {
        console.error('Scanner start error:', err);
        setIsScanning(false);
      }
    };

    const timeout = setTimeout(initScanner, 100);

    return () => {
      clearTimeout(timeout);
      stopScanner();
    };
  }, [hasRole]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isProcessing || (e.target as HTMLElement)?.tagName === 'INPUT') return;

      if (e.key === 'Enter') {
        if (scanBuffer.trim() !== '') {
          console.log('Scanner gun input:', scanBuffer.trim());
          handleQrScanSuccess(scanBuffer.trim());
          setScanBuffer('');
        }
      } else if (e.key.length === 1) {
        setScanBuffer(prev => prev + e.key);

        if (scanBufferTimeout.current) clearTimeout(scanBufferTimeout.current);
        scanBufferTimeout.current = window.setTimeout(() => setScanBuffer(''), 2000);
      }
    };

    const forceFocus = () => {
      if (scanInputRef.current) {
        scanInputRef.current.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    const interval = setInterval(forceFocus, 1000);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      if (scanBufferTimeout.current) clearTimeout(scanBufferTimeout.current);
      clearInterval(interval);
    };
  }, [scanBuffer, isProcessing]);

  useEffect(() => {
    return () => {
      stopScanner();
    };
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isScanning) {
        stopScanner();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isScanning]);

  if (!(hasRole('doctor') || hasRole('admin'))) {
    return <div className="p-6 text-center text-red-500">Access Denied</div>;
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/40 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl p-6 border border-gray-200">
        <button
          onClick={() => stopAndNavigate('/list')}
          className="absolute top-4 right-4 text-gray-500 hover:text-red-600 hover:scale-110 transition z-10"
          disabled={isProcessing}
        >
          <FaTimes className="w-6 h-6" />
        </button>

        <div className="mb-6 text-center">
          <h2 className="text-3xl font-bold text-gray-800">Scan Patient QR Code</h2>
          <p className="text-base text-gray-500 mt-2">Use your camera or scanner gun to scan the QR code</p>
          <FaCamera className="w-8 h-8 mt-3 text-indigo-600 mx-auto" />
        </div>

        <div
          id={scannerId}
          className="w-full h-[500px] max-w-2xl mx-auto border-2 border-dashed border-gray-300 rounded-xl overflow-hidden bg-gray-50 flex items-center justify-center"
        />

        <input
          ref={scanInputRef}
          type="text"
          style={{ position: 'absolute', opacity: 0, height: 0, width: 0, pointerEvents: 'none' }}
          autoFocus
          tabIndex={-1}
        />

        <div className="mt-6 text-center">
          {isProcessing && (
            <div className="flex items-center justify-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
              <p className="text-base text-indigo-600 font-medium">Processing QR code...</p>
            </div>
          )}
          {isScanning && !isProcessing && (
            <div className="space-y-2">
              <p className="text-base text-green-600 font-medium">
                📱 Camera is active - Point QR code at the scanner area
              </p>
              <p className="text-sm text-gray-500">🔫 Scanner gun is also ready for input</p>
            </div>
          )}
          {!isScanning && !isProcessing && (
            <p className="text-base text-gray-500">Starting camera...</p>
          )}
        </div>

        {typeof window !== 'undefined' && window.location.hostname === 'localhost' && scanBuffer && (
          <div className="mt-3 text-center">
            <p className="text-sm text-gray-400">Scanner Buffer: {scanBuffer}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QrScanner;
