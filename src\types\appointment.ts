import {
  AppointmentStatus,
  AppointmentType,
  AppointmentPriority,
  RecurringPattern,
  SlotDuration
} from "./appointmentenums";

// Core Appointment Interface
export interface Appointment {
  appointmentId?: string;
  patientId: string;
  providerId: string;
  facilityId: string;
  
  // Appointment Details
  appointmentDate: string; // ISO datetime string (YYYY-MM-DDTHH:MM:SS) or date string (YYYY-MM-DD)
  startTime: string; // ISO datetime string (YYYY-MM-DDTHH:MM:SS) or time string (HH:MM)
  endTime: string; // ISO datetime string (YYYY-MM-DDTHH:MM:SS) or time string (HH:MM)
  duration: SlotDuration;
  slotNumber?: number; // From /api/consultants/{consultantId}/slots
  
  // Classification
  type: AppointmentType;
  status: AppointmentStatus;
  priority: AppointmentPriority;
  
  // Description & Notes
  title?: string;
  description?: string;
  notes?: string;
  reason?: string;
  
  // Recurring Appointments
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  recurringEndDate?: string; // ISO datetime string (YYYY-MM-DDTHH:MM:SS) or date string (YYYY-MM-DD)
  parentAppointmentId?: string; // For recurring appointments

  // External System Integration
  externalSystemId?: string;
  externalSystemName?: string;

  // Metadata
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  
  // Patient & Provider Info (populated from relations)
  patient?: {
    firstName?: string;
    lastName?: string;
    fullName?: string; // From appointments/query API response
    mobileNumber?: string;
    email?: string;
  };

  provider?: {
    firstName?: string;
    lastName?: string;
    fullName?: string; // From appointments/query API response
    specialization?: string;
    title?: string;
  };
  
  facility?: {
    facilityName?: string;
    address?: string;
  };
}

// Appointment Creation Payload
export interface CreateAppointmentPayload {
  patientId: string;
  providerId: string;
  facilityId: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  duration: SlotDuration;
  slotNumber?: number; // From /api/consultants/{consultantId}/slots
  type: AppointmentType;
  priority: AppointmentPriority;
  title?: string;
  description?: string;
  notes?: string;
  reason?: string;
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  recurringEndDate?: string;
}

// Appointment Update Payload
export interface UpdateAppointmentPayload extends Partial<CreateAppointmentPayload> {
  status?: AppointmentStatus;
  notes?: string;
}

// Appointment Search/Filter Parameters
export interface AppointmentFilters {
  patientId?: string;
  providerId?: string;
  facilityId?: string;
  status?: AppointmentStatus[];
  type?: AppointmentType[];
  priority?: AppointmentPriority[];
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
  page?: number;
  size?: number;
}

// Time Slot Interface
export interface TimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  appointmentId?: string;
  isBlocked?: boolean;
  blockReason?: string;
}

// Provider Schedule Interface
export interface ProviderSchedule {
  providerId: string;
  date: string;
  timeSlots: TimeSlot[];
  workingHours: {
    startTime: string;
    endTime: string;
  };
  isWorkingDay: boolean;
  specialNotes?: string;
}

// Appointment Statistics
export interface AppointmentStats {
  totalAppointments: number;
  todayAppointments: number;
  upcomingAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  noShowAppointments: number;
  statusBreakdown: Record<AppointmentStatus, number>;
  typeBreakdown: Record<AppointmentType, number>;
}

// Default appointment payload
export const defaultAppointmentPayload: CreateAppointmentPayload = {
  patientId: "",
  providerId: "",
  facilityId: "",
  appointmentDate: "",
  startTime: "",
  endTime: "",
  duration: SlotDuration.Thirty,
  slotNumber: undefined,
  type: AppointmentType.Consultation,
  priority: AppointmentPriority.Normal,
  title: "",
  description: "",
  notes: "",
  reason: "",
  isRecurring: false,
  recurringPattern: RecurringPattern.None,
  recurringEndDate: ""
};
