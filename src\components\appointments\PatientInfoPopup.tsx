import React, { useState, useEffect } from 'react';
import { User, Phone, Mail, Calendar, Clock, X } from 'lucide-react';
import { getAppointmentById } from '../../services/appointmentApis';
import { getPatientById } from '../../services/patientApis';

interface PatientInfo {
  patientId: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  mobileNumber?: string;
  email?: string;
  age?: number;
  gender?: string;
}

interface AppointmentInfo {
  appointmentId: string;
  startTime: string;
  endTime: string;
  date: string;
  status: string;
  type?: string;
  notes?: string;
}

interface PatientInfoPopupProps {
  isVisible: boolean;
  onClose: () => void;
  appointmentId?: string;
  slotInfo: {
    startTime: string;
    endTime: string;
    date: string;
    blockReason?: string;
    isBlocked?: boolean;
  };
  position: {
    x: number;
    y: number;
  };
}

export const PatientInfoPopup: React.FC<PatientInfoPopupProps> = ({
  isVisible,
  onClose,
  appointmentId,
  slotInfo,
  position
}) => {
  const [patientInfo, setPatientInfo] = useState<PatientInfo | null>(null);
  const [appointmentInfo, setAppointmentInfo] = useState<AppointmentInfo | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isVisible && (appointmentId || slotInfo.isBlocked)) {
      loadPatientInfo();
    }
  }, [isVisible, appointmentId, slotInfo.isBlocked]);

  const loadPatientInfo = async () => {
    setLoading(true);
    try {
      if (slotInfo.isBlocked) {
        // For blocked slots, show provider block information
        setPatientInfo(null); // No patient info for blocked slots
        const blockAppointmentInfo: AppointmentInfo = {
          appointmentId: "BLOCKED",
          startTime: slotInfo.startTime,
          endTime: slotInfo.endTime,
          date: slotInfo.date,
          status: "Blocked",
          type: "Provider Block",
          notes: slotInfo.blockReason || "This slot has been blocked by the provider"
        };
        setAppointmentInfo(blockAppointmentInfo);
      } else if (appointmentId) {
        // Fetch real appointment data from API
        try {
          const appointment = await getAppointmentById(appointmentId);

          // Set appointment info
          const appointmentInfo: AppointmentInfo = {
            appointmentId: appointment.appointmentId || appointmentId,
            startTime: appointment.startTime,
            endTime: appointment.endTime,
            date: appointment.appointmentDate,
            status: appointment.status,
            type: appointment.type,
            notes: appointment.notes || appointment.description
          };
          setAppointmentInfo(appointmentInfo);

          // Fetch patient details if patientId is available
          if (appointment.patientId) {
            try {
              const patientResponse = await getPatientById(appointment.patientId);

              if (patientResponse && (Array.isArray(patientResponse) ? patientResponse.length > 0 : patientResponse.data)) {
                const patientData = Array.isArray(patientResponse) ? patientResponse[0] : patientResponse.data || patientResponse;

                const patientInfo: PatientInfo = {
                  patientId: patientData.patientId || patientData.upId || appointment.patientId,
                  fullName: patientData.fullName || `${patientData.firstName || ''} ${patientData.lastName || ''}`.trim(),
                  firstName: patientData.firstName,
                  lastName: patientData.lastName,
                  mobileNumber: patientData.mobileNumber || patientData.phoneNumber,
                  email: patientData.email,
                  age: patientData.age,
                  gender: patientData.gender
                };
                setPatientInfo(patientInfo);
              } else {
                // Use patient info from appointment if available
                if (appointment.patient) {
                  const patientInfo: PatientInfo = {
                    patientId: appointment.patientId,
                    fullName: appointment.patient.fullName || `${appointment.patient.firstName || ''} ${appointment.patient.lastName || ''}`.trim(),
                    firstName: appointment.patient.firstName,
                    lastName: appointment.patient.lastName
                  };
                  setPatientInfo(patientInfo);
                }
              }
            } catch (patientError) {
              console.warn('Failed to fetch patient details:', patientError);
              // Use patient info from appointment if available
              if (appointment.patient) {
                const patientInfo: PatientInfo = {
                  patientId: appointment.patientId,
                  fullName: appointment.patient.fullName || `${appointment.patient.firstName || ''} ${appointment.patient.lastName || ''}`.trim(),
                  firstName: appointment.patient.firstName,
                  lastName: appointment.patient.lastName
                };
                setPatientInfo(patientInfo);
              }
            }
          }
        } catch (appointmentError) {
          console.error('Failed to fetch appointment details:', appointmentError);
          // Fallback to basic slot info
          const fallbackAppointmentInfo: AppointmentInfo = {
            appointmentId: appointmentId,
            startTime: slotInfo.startTime,
            endTime: slotInfo.endTime,
            date: slotInfo.date,
            status: "Unknown",
            type: "Appointment",
            notes: "Unable to load appointment details"
          };
          setAppointmentInfo(fallbackAppointmentInfo);
        }
      } else {
        // No appointment ID provided
        const fallbackAppointmentInfo: AppointmentInfo = {
          appointmentId: "UNKNOWN",
          startTime: slotInfo.startTime,
          endTime: slotInfo.endTime,
          date: slotInfo.date,
          status: "Booked",
          type: "Appointment",
          notes: "Appointment details not available"
        };
        setAppointmentInfo(fallbackAppointmentInfo);
      }
    } catch (error) {
      console.error('Failed to load slot info:', error);
      // Set error state
      const errorAppointmentInfo: AppointmentInfo = {
        appointmentId: appointmentId || "ERROR",
        startTime: slotInfo.startTime,
        endTime: slotInfo.endTime,
        date: slotInfo.date,
        status: "Error",
        type: "Error",
        notes: "Failed to load appointment information"
      };
      setAppointmentInfo(errorAppointmentInfo);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (time: string) => {
    return time.substring(0, 5); // Convert HH:MM:SS to HH:MM
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 z-40" 
        onClick={onClose}
      />
      
      {/* Popup */}
      <div
        className="fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 max-w-sm w-80"
        style={{
          left: Math.min(position.x, window.innerWidth - 320),
          top: Math.min(position.y, window.innerHeight - 300),
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-800 flex items-center">
            <User size={16} className={`mr-2 ${slotInfo.isBlocked ? 'text-gray-600' : 'text-blue-600'}`} />
            {slotInfo.isBlocked ? 'Blocked Slot Details' : 'Appointment Details'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={16} />
          </button>
        </div>

        {loading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-xs text-gray-500 mt-2">Loading...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Patient Info */}
            {patientInfo && (
              <div className="bg-blue-50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Patient Information</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center">
                    <User size={12} className="mr-2 text-blue-600" />
                    <span className="font-medium">{patientInfo.fullName || `${patientInfo.firstName} ${patientInfo.lastName}`}</span>
                  </div>
                  {patientInfo.mobileNumber && (
                    <div className="flex items-center">
                      <Phone size={12} className="mr-2 text-blue-600" />
                      <span>{patientInfo.mobileNumber}</span>
                    </div>
                  )}
                  {patientInfo.email && (
                    <div className="flex items-center">
                      <Mail size={12} className="mr-2 text-blue-600" />
                      <span>{patientInfo.email}</span>
                    </div>
                  )}
                  {patientInfo.age && patientInfo.gender && (
                    <div className="text-gray-600">
                      {patientInfo.age} years, {patientInfo.gender}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Appointment/Block Info */}
            {appointmentInfo && (
              <div className={`rounded-lg p-3 ${slotInfo.isBlocked ? 'bg-gray-50' : 'bg-green-50'}`}>
                <h4 className={`text-sm font-medium mb-2 ${slotInfo.isBlocked ? 'text-gray-800' : 'text-green-800'}`}>
                  {slotInfo.isBlocked ? 'Block Details' : 'Appointment Details'}
                </h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center">
                    <Calendar size={12} className={`mr-2 ${slotInfo.isBlocked ? 'text-gray-600' : 'text-green-600'}`} />
                    <span>{formatDate(appointmentInfo.date)}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock size={12} className={`mr-2 ${slotInfo.isBlocked ? 'text-gray-600' : 'text-green-600'}`} />
                    <span>{formatTime(appointmentInfo.startTime)} - {formatTime(appointmentInfo.endTime)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      appointmentInfo.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                      appointmentInfo.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      appointmentInfo.status === 'Blocked' ? 'bg-gray-100 text-gray-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {appointmentInfo.status}
                    </span>
                  </div>
                  {appointmentInfo.type && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span>{appointmentInfo.type}</span>
                    </div>
                  )}
                  {appointmentInfo.notes && (
                    <div className={`mt-2 pt-2 border-t ${slotInfo.isBlocked ? 'border-gray-200' : 'border-green-200'}`}>
                      <span className="text-gray-600 text-xs">{slotInfo.isBlocked ? 'Reason:' : 'Notes:'}</span>
                      <p className="text-gray-700 text-xs mt-1">{appointmentInfo.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};
