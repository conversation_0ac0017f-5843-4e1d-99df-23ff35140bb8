@import "tailwindcss";
@plugin "daisyui";

/* Force blur effects with maximum specificity - WORKING SOLUTION - Updated */
.blur-backdrop {
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  -moz-backdrop-filter: blur(8px) !important;
  -ms-backdrop-filter: blur(8px) !important;
}

.blur-backdrop-light {
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  -moz-backdrop-filter: blur(4px) !important;
  -ms-backdrop-filter: blur(4px) !important;
}

/* Override Tailwind with element specificity */
div.blur-backdrop {
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

div.blur-backdrop-light {
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Fallback for unsupported browsers */
@supports not (backdrop-filter: blur(1px)) {
  .blur-backdrop,
  .blur-backdrop-light {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }
}

/* Additional specificity for hosted environments */
.fixed.inset-0.blur-backdrop {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.fixed.inset-0.blur-backdrop-light {
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
}

/* Force blur with highest specificity */
div[class*="blur-backdrop"] {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}