import { AppointmentPriority, AppointmentType } from './appointmentenums';

// Queue Status Enum
export enum QueueStatus {
  Waiting = "Waiting",
  Called = "Called", 
  InService = "InService",
  Completed = "Completed",
  NoShow = "NoShow",
  Skipped = "Skipped"
}

// Service Type Enum
export enum ServiceType {
  Consultation = "Consultation",
  Emergency = "Emergency",
  Procedure = "Procedure",
  Diagnostic = "Diagnostic",
  Pharmacy = "Pharmacy",
  Laboratory = "Laboratory",
  Radiology = "Radiology",
  Registration = "Registration",
  Cardiology = "Cardiology",
  Orthopedics = "Orthopedics",
  Pediatrics = "Pediatrics",
  Gynecology = "Gynecology",
  Dermatology = "Dermatology"
}

// Queue Entry Interface
export interface QueueEntry {
  queueId: string;
  appointmentId?: string;
  patientId: string;
  patientName: string;
  queueNumber: number;
  serviceType: ServiceType;
  priority: AppointmentPriority;
  status: QueueStatus;
  estimatedWaitTime: number; // in minutes
  estimatedServiceTime: string; // HH:MM format
  actualWaitTime?: number; // in minutes
  actualServiceTime?: number; // in minutes
  joinedAt: string; // ISO timestamp
  calledAt?: string; // ISO timestamp
  serviceStartedAt?: string; // ISO timestamp
  serviceCompletedAt?: string; // ISO timestamp
  notes?: string;
  facilityId: string;
  providerId?: string;
  
  // Patient details
  patient?: {
    firstName: string;
    lastName: string;
    mobileNumber?: string;
    age?: number;
    gender?: string;
  };
  
  // Provider details
  provider?: {
    firstName: string;
    lastName: string;
    title: string;
    specialization: string;
  };
}

// Queue Summary Interface
export interface QueueSummary {
  serviceType: ServiceType;
  facilityId: string;
  date: string;
  totalInQueue: number;
  currentlyServing?: {
    queueEntry: QueueEntry;
    serviceStartTime: string;
  };
  queue: QueueEntry[];
  averageServiceTime: number; // in minutes
  estimatedWaitTime: number; // in minutes for next patient
  peakHours: string[];
  lastUpdated: string;
}

// Wait Time Estimation Interface
export interface WaitTimeEstimation {
  serviceType: ServiceType;
  facilityId: string;
  estimatedWaitTime: number; // in minutes
  queuePosition: number;
  patientsAhead: number;
  averageServiceTime: number;
  currentWaitTime: {
    [key in AppointmentPriority]: number;
  };
  lastUpdated: string;
  confidence: number; // 0-100 percentage
}

// Queue Statistics Interface
export interface QueueStatistics {
  facilityId: string;
  date: string;
  totalProcessed: number;
  currentInQueue: number;
  averageWaitTime: number;
  averageServiceTime: number;
  peakHours: string[];
  serviceStats: {
    [key in ServiceType]?: {
      totalProcessed: number;
      averageWaitTime: number;
      averageServiceTime: number;
      currentInQueue: number;
    };
  };
  hourlyStats: Array<{
    hour: string;
    patientsServed: number;
    averageWaitTime: number;
    queueLength: number;
  }>;
  patientSatisfactionScore?: number;
  noShowRate: number;
}

// Queue Filters Interface
export interface QueueFilters {
  facilityId?: string;
  serviceType?: ServiceType[];
  status?: QueueStatus[];
  priority?: AppointmentPriority[];
  dateFrom?: string;
  dateTo?: string;
  providerId?: string;
}

// Queue Management Actions
export interface QueueAction {
  type: 'ADD' | 'UPDATE' | 'REMOVE' | 'CALL' | 'START_SERVICE' | 'COMPLETE_SERVICE';
  queueId: string;
  data?: Partial<QueueEntry>;
  reason?: string;
  performedBy: string;
  timestamp: string;
}

// Real-time Queue Update
export interface QueueUpdate {
  eventType: 'QUEUE_UPDATED' | 'PATIENT_CALLED' | 'SERVICE_STARTED' | 'SERVICE_COMPLETED';
  serviceType: ServiceType;
  facilityId: string;
  queueEntry?: QueueEntry;
  queueSummary?: QueueSummary;
  timestamp: string;
}

// Export all types for easier importing
export type {
  QueueEntry as QueueEntryType,
  QueueSummary as QueueSummaryType,
  QueueStatistics as QueueStatisticsType,
  WaitTimeEstimation as WaitTimeEstimationType,
  QueueFilters as QueueFiltersType,
  QueueAction as QueueActionType,
  QueueUpdate as QueueUpdateType
};
