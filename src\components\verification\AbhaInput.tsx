import React, { useState } from "react";
import { verifyAbhaNumber } from "../../services/abhaApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { usePatientFormStore } from "../../store/patientFormStore";
import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";
import { AbhaMobileVerification } from "./AbhaMobileVerification";
import AbhaQRScanner from "./AbhaQRScanner";

type AbhaInputProps = {
  abhaNumber: string;
  setAbhaNumber: (value: string) => void;
  required?: boolean;
};

const AbhaInput: React.FC<AbhaInputProps> = ({
  abhaNumber,
  setAbhaNumber,
  required = false
}) => {
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<"mobile" | "abha" | "scanner" | "">("abha");
  const [showModal, setShowModal] = useState(false);
  const [scannerOpen, setScannerOpen] = useState(false);

  const isValidMobile = (val: string) => /^\d{10}$/.test(val);
  const isValidAbha = (val: string) =>
    /^\d{14}$/.test(val.replace(/-/g, "")) || /^\d{2}-\d{4}-\d{4}-\d{4}$/.test(val);

  const handleLegacyVerify = async () => {
    if (!abhaNumber || abhaNumber.length !== 14) {
      showError("", "Enter a valid 14-digit ABHA number");
      return;
    }

    setLoading(true);
    try {
      const { success, data, error } = await verifyAbhaNumber({ abhaNumber });

      if (success && data && data.ABHAProfile) {
        const patientData = mapAbhaProfileToPatient(data.ABHAProfile);
        if (patientData) {
          usePatientFormStore.getState().setQuickFormData(patientData);
          showSuccess("ABHA Verification Successful", "Patient data populated from ABHA profile");
        } else {
          showError("Mapping Failed", "Could not map ABHA profile to patient format.");
        }
      } else {
        showError("ABHA Verification Failed", error || "Could not verify ABHA number");
      }
    } catch (err) {
      console.error("Unexpected error during ABHA verification:", err);
      showError("Verification Error", "Unable to verify ABHA number");
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = () => {
    if (!mode) {
      handleLegacyVerify();
      return;
    }

    if (mode === "mobile" && isValidMobile(abhaNumber)) {
      setShowModal(true);
    } else if (mode === "abha" && isValidAbha(abhaNumber)) {
      setShowModal(true);
    } else {
      showError("Invalid Input", "Please enter a valid number.");
    }
  };

  const isProceedDisabled = () => {
    if (!mode) return !abhaNumber || abhaNumber.length !== 14;
    if (mode === "mobile") return !isValidMobile(abhaNumber);
    if (mode === "abha") return !isValidAbha(abhaNumber);
    return true;
  };

  const getPlaceholder = () => {
    if (mode === "mobile") return "Enter 10-digit mobile number";
    if (mode === "abha") return "Enter ABHA (14 digits or formatted)";
    return "Enter ABHA Number";
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, ""); // Remove all non-digit characters

    if (mode === "mobile") {
      if (value.length === 1 && value === "0") {
        value = ""; // Ignore zero as first digit
      }
      // For mobile number: only digits, max 10
      if (value.length > 10) value = value.slice(0, 10);
      setAbhaNumber(value);
    } else if (mode === "abha") {
      // For ABHA number: digits with hyphen formatting, max 14 digits
      if (value.length > 14) value = value.slice(0, 14);

      // Apply formatting: 91-6046-2021-2430
      const formatted = value
        .replace(/^(\d{2})(\d{0,4})(\d{0,4})(\d{0,4})$/, (_, p1, p2, p3, p4) =>
          [p1, p2, p3, p4].filter(Boolean).join("-")
        );

      setAbhaNumber(formatted);
    } else {
      // Default case (legacy): max 14 digits, no formatting
      if (value.length > 14) value = value.slice(0, 14);
      setAbhaNumber(value);
    }
  };

  return (
    <div className="w-full max-w-4xl">
      <div className="mb-4">
        {/* <h2 className="text-lg font-semibold text-blue-700 mb-1">ABHA Verification</h2>
        <h3 className="text-sm font-medium text-gray-700">
          Generate ABHA Number Using: {required && <span className="text-red-500">*</span>}
        </h3> */}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Select Method
          </label>
          <select
            value={mode}
            onChange={(e) => {
              const newMode = e.target.value as "mobile" | "abha" | "scanner";
              setMode(newMode);
              // Clear the input when switching modes
              setAbhaNumber("");
              setScannerOpen(newMode === "scanner");
            }}
            className="w-full h-10 border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md appearance-none cursor-pointer"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
              backgroundPosition: 'right 10px center',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '16px'
            }}
          >
            <option value="mobile">Mobile Number</option>
            <option value="abha">ABHA Number</option>
            <option value="scanner">ABHA Scanner</option>
          </select>
        </div>

        {
          mode != "scanner" && (
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                {mode === "mobile" ? "Mobile Details" : "ABHA Details"}
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={abhaNumber}
                  onChange={handleInputChange}
                  placeholder={getPlaceholder()}
                  className="flex-1 h-10 border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed"
                  disabled={loading}
                  maxLength={mode === "mobile" ? 10 : 17} // 17 for ABHA with hyphens (14 digits + 3 hyphens)
                />
                <button
                  type="button"
                  onClick={handleVerify}
                  disabled={loading || isProceedDisabled()}
                  className="h-10 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-semibold rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg min-w-[100px]"
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Verifying</span>
                    </div>
                  ) : (
                    "Verify"
                  )}
                </button>
              </div>
            </div>
          )
        }

      </div>

      <div className="mt-4">
        {/* {mode === "abha" && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-md">
            <p className="text-xs text-blue-700">
              Use ABHA Number to verify existing patient records and populate profile information automatically.
            </p>
          </div>
        )} */}
        {/* {mode === "mobile" && (
          <div className="bg-green-50 border-l-4 border-green-400 p-3 rounded-md">
            <p className="text-xs text-green-700">
              Login using ABHA-linked mobile number for quick verification and profile access.
            </p>
          </div>
        )} */}
        {!mode && (
          <div className="bg-gray-50 border-l-4 border-gray-400 p-3 rounded-md">
            <p className="text-xs text-gray-700">
              Use ABHA number for direct verification and automatic profile population.
            </p>
          </div>
        )}
      </div>

      {/* <div className="mt-4 pt-2 border-t border-gray-200">
        <p className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer transition-colors duration-200">
          Already have an ABHA Number? Verify here
        </p>
      </div> */}

      {mode === "scanner" && scannerOpen && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="bg-gray-100 border rounded p-4 text-sm text-gray-600">
            Scan a valid ABHA QR Code using the camera on the right. Patient data will auto-fill on success.
          </div>
          <AbhaQRScanner onClose={() => setScannerOpen(false)} /> {/* Updated */}
        </div>
      )}

      {showModal && (
        <AbhaMobileVerification
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          inputValue={abhaNumber}
          mode={mode as "mobile" | "abha"}
        />
      )}
    </div>
  );
};

export default AbhaInput;