import type { ChangeEvent } from "react";

type SetFormFn<T> = React.Dispatch<React.SetStateAction<T>>;

/**
 * Creates a change handler that updates nested form values
 * and validates the updated field in real-time.
 */
export const createChangeHandler = <T>(
  setForm: SetFormFn<T>,
  validateField: (path: string, value: any, updatedForm: T) => boolean
) => {
  return (fieldPath: string) => (
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const target = e.target as HTMLInputElement;
    const { type, checked, value } = target;
    const val = type === "checkbox" ? checked : value;

    setForm(prev => {
      const updatedForm = setNestedValue({ ...prev }, fieldPath, val);
      validateField(fieldPath, val, updatedForm); // Validate immediately
      return updatedForm;
    });
  };
};

/**
 * Safely sets a nested value using dot notation (e.g., 'contacts.0.phoneNumber')
 */
function setNestedValue(obj: any, path: string, value: any) {
  const keys = path.split(".");
  const lastKey = keys.pop();
  let current = obj;

  for (const key of keys) {
    if (!current[key]) current[key] = isNaN(Number(key)) ? {} : [];
    current = current[key];
  }

  if (lastKey !== undefined) {
    current[lastKey] = value;
  }

  return obj;
}
