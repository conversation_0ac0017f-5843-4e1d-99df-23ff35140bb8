import Select from "react-select";

 export const ReactSelectInput = ({ label, name, value, onChange, options }) => {
  const formattedOptions = options.map((opt) => ({ label: opt, value: opt }));
  const selectedOption = formattedOptions.find((opt) => opt.value === value) || null;

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <Select
        name={name}
        options={formattedOptions}
        value={selectedOption}
        onChange={(selected) =>
          onChange({ target: { name, value: selected ? selected.value : "" } })
        }
        isClearable
        className="react-select-container"
        classNamePrefix="react-select"
      />
    </div>
  );
};
