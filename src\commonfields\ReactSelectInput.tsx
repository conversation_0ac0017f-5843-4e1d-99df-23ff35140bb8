import Select from "react-select";
import type { MultiValue, SingleValue } from "react-select";


type OptionType = { label: string; value: string };

type ReactSelectInputProps = {
  label: string;
  name: string;
  value: string | string[];
  onChange: (e: { target: { name: string; value: string | string[] } }) => void;
  options: string[];
  isMulti?: boolean;
  error?: string;
};

export const ReactSelectInput: React.FC<ReactSelectInputProps> = ({
  label,
  name,
  value,
  onChange,
  options,
  isMulti = false,
  error
}) => {
  const formattedOptions: OptionType[] = options.map((opt) => ({ label: opt, value: opt }));

  const selectedOption = isMulti
    ? formattedOptions.filter((opt) => Array.isArray(value) && value.includes(opt.value))
    : formattedOptions.find((opt) => opt.value === value) || null;

  const handleChange = (
    selected: MultiValue<OptionType> | SingleValue<OptionType>
  ) => {
    if (isMulti) {
      const multi = selected as MultiValue<OptionType>;
      const selectedValues = multi.map((opt) => opt.value);
      onChange({ target: { name, value: selectedValues } });
    } else {
      const single = selected as SingleValue<OptionType>;
      onChange({ target: { name, value: single?.value ?? "" } });
    }
  };

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <Select
        name={name}
        options={formattedOptions}
        value={selectedOption}
        onChange={handleChange}
        isClearable
        isMulti={isMulti}
        className={`react-select-container ${error ? "border border-red-500 rounded" : ""}`}
        classNamePrefix="react-select"
      />
      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  );
};
