import Select from "react-select";

export const ReactSelectInput = ({
  label,
  name,
  value,
  onChange,
  options,
  isMulti = false // allow enabling multi-select
}) => {
  const formattedOptions = options.map((opt) => ({ label: opt, value: opt }));

  const selectedOption = isMulti
    ? formattedOptions.filter((opt) => value.includes(opt.value))
    : formattedOptions.find((opt) => opt.value === value) || null;

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <Select
        name={name}
        options={formattedOptions}
        value={selectedOption}
        onChange={(selected) => {
          const selectedValue = isMulti
            ? selected?.map((opt) => opt.value) || []
            : selected?.value || "";
          onChange({ target: { name, value: selectedValue } });
        }}
        isClearable
        isMulti={isMulti}
        className="react-select-container"
        classNamePrefix="react-select"
      />
    </div>
  );
};
