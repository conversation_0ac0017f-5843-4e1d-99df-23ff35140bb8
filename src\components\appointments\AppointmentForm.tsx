import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { appointmentSchema, type AppointmentFormData, type AppointmentFormInput } from "../../zod_validations/appointment/appointmentSchema";
import { FormField } from "../../commonfields/FormField";
import { Input } from "../../commonfields/Input";
import { Select } from "../../commonfields/Select";
import { Calendar } from "../../commonfields/Calendar";
import { Button } from "../../commonfields/Button";
import FormMessage from "../../commonfields/FormMessage";
import FacilitySelector from "../../commonfields/FacilitySelector";
import { SlotSelectionPanel } from "../schedule/SlotSelectionPanel";
import { HelpCircle } from "lucide-react";
import {
  appointmentTypeOptions,
  appointmentPriorityOptions,
  slotDurationOptions,
  recurringPatternOptions,
  SlotDuration,
  AppointmentType,
  AppointmentPriority,
  RecurringPattern
} from "../../types/appointmentenums";
import { createAppointment, updateAppointment } from "../../services/appointmentApis";
import { getAvailableDoctors } from "../../services/scheduleApis";
import { searchPatientsForDropdown } from "../../services/patientApis";
import { fetchFacilities } from "../../services/facilityApi";
import { showError } from "../../utils/toastUtils";
import { useAppointmentStore } from "../../store/appointmentStore";
import type { Appointment } from "../../types/appointment";
import type { AvailableSlot } from "../../types/schedule";

interface AppointmentFormProps {
  appointmentId?: string;
  initialData?: Partial<Appointment>;
  onSuccess?: (appointment?: Appointment) => void;
  onCancel?: () => void;
  selectedDate?: string;
  selectedTime?: string;
  onProviderChange?: (providerId: string) => void;
  bookedSlots?: string[];
  isSlotBooked?: (time: string) => boolean;
  editingAppointment?: Appointment | null;
}

// Helper functions for default values
const getCurrentDate = () => {
  return new Date().toISOString().split('T')[0];
};

const getCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

const CIVIL_HOSPITAL_SHILLONG_ID = "civil-hospital-shillong"; // Default facility ID

export const AppointmentForm: React.FC<AppointmentFormProps> = ({
  appointmentId,
  initialData,
  onSuccess,
  onCancel,
  selectedDate,
  selectedTime,
  onProviderChange,
  bookedSlots = [],
  isSlotBooked,
  editingAppointment
}) => {
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<any[]>([]);
  const [patients, setPatients] = useState<any[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(false);
  const [loadingPatients, setLoadingPatients] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<AvailableSlot | null>(null);
  const [showSlotPanel, setShowSlotPanel] = useState(false);
  const [civilHospitalId, setCivilHospitalId] = useState<string>(CIVIL_HOSPITAL_SHILLONG_ID);

  const { addAppointment, updateAppointment: updateAppointmentInStore } = useAppointmentStore();
  const isEditMode = !!appointmentId || !!editingAppointment;
  const appointmentData = editingAppointment || initialData;

  // Helper function to extract date from datetime string
  const extractDateFromDateTime = (dateTime: string): string => {
    if (!dateTime) return "";
    // If it's already in YYYY-MM-DD format, return as is
    if (dateTime.match(/^\d{4}-\d{2}-\d{2}$/)) return dateTime;
    // If it's in datetime format, extract the date part
    return dateTime.split('T')[0];
  };

  // Helper function to extract time from datetime string
  const extractTimeFromDateTime = (dateTime: string): string => {
    if (!dateTime) return "";
    // If it contains 'T', extract time part
    if (dateTime.includes('T')) {
      const timePart = dateTime.split('T')[1];
      // Remove seconds if present
      return timePart.split(':').slice(0, 2).join(':');
    }
    // If it's already in HH:MM format, return as is
    if (dateTime.match(/^\d{2}:\d{2}$/)) return dateTime;
    return "";
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<AppointmentFormInput>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      patientId: "", // Will be set by useEffect when data is loaded
      providerId: "", // Will be set by useEffect when data is loaded
      facilityId: appointmentData?.facilityId || civilHospitalId, // Auto-select Civil Hospital Shillong
      appointmentDate: selectedDate || extractDateFromDateTime(appointmentData?.appointmentDate || "") || getCurrentDate(), // Default to today
      startTime: selectedTime || extractTimeFromDateTime(appointmentData?.startTime || "") || getCurrentTime(), // Default to current time
      endTime: extractTimeFromDateTime(appointmentData?.endTime || ""),
      duration: appointmentData?.duration?.toString() || SlotDuration.Thirty.toString(),
      type: appointmentData?.type || AppointmentType.Consultation,
      priority: appointmentData?.priority || AppointmentPriority.Normal,
      title: appointmentData?.title || "",
      description: appointmentData?.description || "",
      notes: appointmentData?.notes || "",
      reason: appointmentData?.reason || "",
      isRecurring: appointmentData?.isRecurring || false,
      recurringPattern: appointmentData?.recurringPattern || RecurringPattern.None,
      recurringEndDate: extractDateFromDateTime(appointmentData?.recurringEndDate || "")
    }
  });

  const watchIsRecurring = watch("isRecurring");
  const watchStartTime = watch("startTime");
  const watchDuration = watch("duration");

  // Load providers and patients on component mount
  useEffect(() => {
    loadProviders();
    loadPatients();
    loadCivilHospitalId();
  }, []);

  // Force form reset when appointment data changes (for edit mode)
  useEffect(() => {
    if (isEditMode && appointmentData) {
      console.log("🔄 Resetting form for edit mode");
      reset({
        patientId: "", // Will be set by the other useEffect
        providerId: "", // Will be set by the other useEffect
        facilityId: appointmentData.facilityId || civilHospitalId,
        appointmentDate: extractDateFromDateTime(appointmentData.appointmentDate || "") || getCurrentDate(),
        startTime: extractTimeFromDateTime(appointmentData.startTime || "") || getCurrentTime(),
        endTime: extractTimeFromDateTime(appointmentData.endTime || ""),
        duration: appointmentData.duration?.toString() || SlotDuration.Thirty.toString(),
        type: appointmentData.type || AppointmentType.Consultation,
        priority: appointmentData.priority || AppointmentPriority.Normal,
        title: appointmentData.title || "",
        description: appointmentData.description || "",
        notes: appointmentData.notes || "",
        reason: appointmentData.reason || "",
        isRecurring: appointmentData.isRecurring || false,
        recurringPattern: appointmentData.recurringPattern || RecurringPattern.None,
        recurringEndDate: extractDateFromDateTime(appointmentData.recurringEndDate || "")
      });
    }
  }, [isEditMode, appointmentData, reset, civilHospitalId]);

  const loadCivilHospitalId = async () => {
    try {
      const facilities = await fetchFacilities("Civil Hospital Shillong");
      const civilHospital = facilities.find((facility: any) =>
        facility.facilityName?.toLowerCase().includes("civil hospital shillong") ||
        facility.facilityName?.toLowerCase().includes("civil hospital") ||
        facility.facilityName?.toLowerCase().includes("shillong")
      );

      if (civilHospital) {
        console.log("Found Civil Hospital Shillong:", civilHospital);
        setCivilHospitalId(civilHospital.hospitalId);
        // Update form default value if not in edit mode
        if (!isEditMode && !appointmentData?.facilityId) {
          setValue("facilityId", civilHospital.hospitalId);
        }
      } else {
        console.warn("Civil Hospital Shillong not found in facilities, using fallback ID");
      }
    } catch (error) {
      console.error("Failed to load Civil Hospital Shillong ID:", error);
    }
  };

  // Update form when selectedDate or selectedTime changes
  useEffect(() => {
    if (selectedDate) {
      setValue("appointmentDate", selectedDate);
    }
    if (selectedTime) {
      setValue("startTime", selectedTime);
    }
  }, [selectedDate, selectedTime, setValue]);

  // Auto-calculate end time when start time or duration changes
  useEffect(() => {
    if (watchStartTime && watchDuration) {
      const [hours, minutes] = watchStartTime.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + Number(watchDuration);
      const endHours = Math.floor(endMinutes / 60);
      const endMins = endMinutes % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      setValue("endTime", endTime);
    }
  }, [watchStartTime, watchDuration, setValue]);

  // Update form values when editing appointment and data is loaded
  useEffect(() => {
    if (isEditMode && appointmentData) {
      console.log("🔄 Edit mode detected with appointment data:", {
        appointmentId: appointmentData.appointmentId,
        providerId: appointmentData.providerId,
        patientId: appointmentData.patientId,
        providersLoaded: providers.length,
        patientsLoaded: patients.length
      });

      // Update basic fields immediately (don't wait for providers/patients)
      if (appointmentData.facilityId) setValue("facilityId", appointmentData.facilityId);
      if (appointmentData.appointmentDate) setValue("appointmentDate", extractDateFromDateTime(appointmentData.appointmentDate));
      if (appointmentData.startTime) setValue("startTime", extractTimeFromDateTime(appointmentData.startTime));
      if (appointmentData.endTime) setValue("endTime", extractTimeFromDateTime(appointmentData.endTime));
      if (appointmentData.duration) setValue("duration", appointmentData.duration.toString());
      if (appointmentData.type) setValue("type", appointmentData.type);
      if (appointmentData.priority) setValue("priority", appointmentData.priority);
      if (appointmentData.title) setValue("title", appointmentData.title);
      if (appointmentData.description) setValue("description", appointmentData.description);
      if (appointmentData.notes) setValue("notes", appointmentData.notes);

      // Handle provider selection when providers are loaded
      if (providers.length > 0 && appointmentData.providerId) {
        console.log("🔍 Looking for provider with ID:", appointmentData.providerId);
        console.log("📋 Available providers:", providers.map(p => ({
          consultantId: p.consultantId,
          fullName: p.fullName
        })));

        // Try to find provider by staffId first
        let provider = providers.find(p => p.staffId === appointmentData.providerId);

        // If not found, try legacy fields (consultantId, doctorId)
        if (!provider) {
          provider = providers.find(p => p.consultantId === appointmentData.providerId);
        }
        if (!provider) {
          provider = providers.find(p => p.doctorId === appointmentData.providerId);
        }

        if (provider) {
          console.log("✅ Found provider for edit:", provider.fullName, "staffId:", provider.staffId);
          setValue("providerId", provider.staffId);
        } else {
          console.warn("❌ Provider not found for edit. ProviderId:", appointmentData.providerId);
          console.warn("Available provider IDs:", providers.map(p => p.consultantId));
        }
      }

      // Handle patient selection when patients are loaded
      if (patients.length > 0 && appointmentData.patientId) {
        console.log("🔍 Looking for patient with ID:", appointmentData.patientId);

        const patient = patients.find(p => p.patientId === appointmentData.patientId);
        if (patient) {
          console.log("✅ Found patient for edit:", patient.firstName, patient.lastName);
          setValue("patientId", patient.patientId);
        } else {
          console.warn("❌ Patient not found for edit:", appointmentData.patientId);
          console.warn("Available patient IDs:", patients.map(p => p.patientId));
        }
      }
    }
  }, [isEditMode, appointmentData, providers, patients, setValue]);

  // Show/hide slot panel based on patient, provider and date selection
  useEffect(() => {
    const patientId = watch("patientId");
    const providerId = watch("providerId");
    const appointmentDate = watch("appointmentDate");

    if (patientId && providerId && appointmentDate) {
      setShowSlotPanel(true);
      console.log("Showing slot panel - Patient, Provider and Date selected");
    } else {
      setShowSlotPanel(false);
      setSelectedSlot(null);
    }
  }, [watch("patientId"), watch("providerId"), watch("appointmentDate")]);

  const loadProviders = async () => {
    setLoadingProviders(true);
    try {
      // Get current date in YYYY-MM-DD format
      const currentDate = new Date().toISOString().split('T')[0];
      console.log("Loading available doctors for date:", currentDate);

      const response = await getAvailableDoctors(currentDate);
      if (response.success && response.data) {
        setProviders(response.data);
        console.log(`Loaded ${response.data.length} available doctors for ${currentDate}`);
      } else {
        console.error("Failed to load available doctors:", response.error);
        showError(response.error || "Failed to load available doctors");
      }
    } catch (error) {
      console.error("Failed to load available doctors:", error);
      showError("Failed to load available doctors");
    } finally {
      setLoadingProviders(false);
    }
  };

  const loadPatients = async () => {
    setLoadingPatients(true);
    try {
      // Use the optimized search function for dropdown
      const response = await searchPatientsForDropdown();
      setPatients(response || []);

      if (response.length === 0) {
        console.warn("No patients found. This might indicate an API issue or empty database.");
      }
    } catch (error) {
      console.error("Failed to load patients:", error);
      showError("Failed to load patients. Please check your connection and try again.");
      setPatients([]); // Set empty array on error
    } finally {
      setLoadingPatients(false);
    }
  };

  const handleSlotSelect = (slot: AvailableSlot) => {
    setSelectedSlot(slot);

    // Auto-populate form fields from selected slot
    setValue("appointmentDate", slot.date);
    setValue("startTime", slot.startTime.substring(0, 5)); // Convert HH:MM:SS to HH:MM
    setValue("endTime", slot.endTime.substring(0, 5));
    setValue("duration", slot.duration.toString());
  };

  const handleProviderChange = (providerId: string) => {
    setValue("providerId", providerId);
    onProviderChange?.(providerId);

    // Show slot panel when patient, provider and date are all selected
    const patientId = watch("patientId");
    const appointmentDate = watch("appointmentDate");

    if (patientId && providerId && appointmentDate) {
      setShowSlotPanel(true);
      console.log("Provider changed - showing slot panel");
    } else {
      setShowSlotPanel(false);
      setSelectedSlot(null);
    }
  };

  const onSubmit = async (data: AppointmentFormInput) => {
    setLoading(true);
    try {
      // Transform all date/time fields to ISO datetime format
      const transformedData = {
        ...data,
        // Convert appointmentDate to datetime format
        appointmentDate: `${data.appointmentDate}T${data.startTime}:00`,
        // Convert startTime to datetime format
        startTime: `${data.appointmentDate}T${data.startTime}:00`,
        // Convert endTime to datetime format
        endTime: `${data.appointmentDate}T${data.endTime}:00`,
        // Convert recurringEndDate to datetime format if present
        recurringEndDate: data.recurringEndDate ? `${data.recurringEndDate}T23:59:59` : data.recurringEndDate,
        // Include slotNumber from selected slot
        slotNumber: selectedSlot?.slotNumber,
        // Ensure proper type casting
        duration: typeof data.duration === 'string' ? parseInt(data.duration) as SlotDuration : data.duration,
        type: data.type as AppointmentType,
        priority: data.priority as AppointmentPriority
      };

      if (isEditMode && (appointmentId || editingAppointment?.appointmentId)) {
        const idToUpdate = appointmentId || editingAppointment?.appointmentId!;
        const result = await updateAppointment(idToUpdate, transformedData as any);
        if (result.success) {
          updateAppointmentInStore(idToUpdate, result.data);
          // Success message will be shown by the parent component
          onSuccess?.(result.data);
        } else {
          showError(result.error || "Failed to update appointment");
        }
      } else {
        const result = await createAppointment(transformedData as any);
        if (result.success) {
          addAppointment(result.data);
          // Success message will be shown by the parent component
          reset();
          onSuccess?.(result.data);
        } else {
          showError(result.error || "Failed to create appointment");
        }
      }
    } catch (error) {
      console.error("Appointment form error:", error);
      showError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Patient Selection */}
        <FormField label="Patient" required>
          <Select {...register("patientId")} disabled={loadingPatients}>
            <option value="">
              {loadingPatients ? "Loading patients..." : "Select Patient"}
            </option>
            {patients.map((patient) => {
              // Create a display name with fallbacks
              const firstName = patient.firstName || "";
              const lastName = patient.lastName || "";
              const fullName = `${firstName} ${lastName}`.trim() || "Unnamed Patient";
              const identifier = patient.identifierNumber || patient.upId || patient.patientId || "";
              const mobile = patient.contacts?.[0]?.mobileNumber || "";

              // Create a comprehensive display string
              let displayText = fullName;
              if (identifier) displayText += ` (${identifier})`;
              if (mobile) displayText += ` - ${mobile}`;

              return (
                <option key={patient.patientId} value={patient.patientId}>
                  {displayText}
                </option>
              );
            })}
          </Select>
          <FormMessage>{errors.patientId?.message}</FormMessage>
          {patients.length === 0 && !loadingPatients && (
            <p className="text-sm text-gray-500 mt-1">
              No patients found. Please ensure patients are registered in the system.
            </p>
          )}
        </FormField>

        {/* Provider Selection */}
        <FormField label="Provider" required>
          <Select
            {...register("providerId")}
            disabled={loadingProviders}
            onChange={(e) => handleProviderChange(e.target.value)}
          >
            <option value="">Select Provider</option>
            {providers.map((doctor) => (
              <option key={doctor.staffId} value={doctor.staffId}>
                {doctor.fullName} - {doctor.primarySpecialization || doctor.specialization || 'No specialization'}
              </option>
            ))}
          </Select>
          <FormMessage>{errors.providerId?.message}</FormMessage>
          {loadingProviders && (
            <p className="text-sm text-gray-500 mt-1">Loading doctors...</p>
          )}
        </FormField>

        {/* Facility Selection */}
        {/* <FormField label="Facility" required>
          <FacilitySelector
            name="facilityId"
            value={watch("facilityId")}
            onChange={(e) => setValue("facilityId", e.target.value)}
            placeholder="Select Facility"
          />
          <FormMessage>{errors.facilityId?.message}</FormMessage>
        </FormField> */}


        {/* Start Time (Disabled - set via slot selection) */}
        {/* <FormField label="Start Time" required>
          <Input
            {...register("startTime")}
            type="time"
            disabled
            className="bg-gray-100"
          />
          <FormMessage>{errors.startTime?.message}</FormMessage>
          <p className="text-sm text-gray-500 mt-1">
            Start time is set automatically when you select a slot
          </p>
        </FormField> */}

        {/* End Time (Auto-calculated, read-only) */}
        {/* <FormField label="End Time">
          <Input {...register("endTime")} type="time" disabled className="bg-gray-100" />
          <FormMessage>{errors.endTime?.message}</FormMessage>
          <p className="text-sm text-gray-500 mt-1">
            End time is calculated automatically based on duration
          </p>
        </FormField> */}

         {/* Duration */}
        {/* <FormField label="Duration" required>
          <Select {...register("duration")}>
            <option value="">Select Duration</option>
            {slotDurationOptions.map((duration) => (
              <option key={duration} value={duration}>
                {duration} minutes
              </option>
            ))}
          </Select>
          <FormMessage>{errors.duration?.message as string}</FormMessage>
        </FormField> */}

        {/* Appointment Date */}
        <FormField label="Appointment Date" required>
          <Calendar {...register("appointmentDate")} />
          <FormMessage>{errors.appointmentDate?.message}</FormMessage>
        </FormField>

        {/* Appointment Type */}
        <FormField label="Type" required>
          <Select {...register("type")}>
            {appointmentTypeOptions.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </Select>
          <FormMessage>{errors.type?.message}</FormMessage>
        </FormField>

        {/* Priority */}
        {/* <FormField label="Priority" required>
          <Select {...register("priority")}>
            {appointmentPriorityOptions.map((priority) => (
              <option key={priority} value={priority}>
                {priority}
              </option>
            ))}
          </Select>
          <FormMessage>{errors.priority?.message}</FormMessage>
        </FormField> */}
      </div>

      {/* Slot Selection Panel */}
      {showSlotPanel && (
        <div className="mt-6">
          <SlotSelectionPanel
            providerId={watch("providerId")} // Now directly using consultantId from the form
            selectedDate={watch("appointmentDate")}
            onSlotSelect={handleSlotSelect}
            selectedSlot={selectedSlot}
            className="mb-6"
          />
        </div>
      )}

      {/* Additional Information */}
      <div className="space-y-4">
        <FormField label="Title">
          <Input {...register("title")} placeholder="Appointment title" />
          <FormMessage>{errors.title?.message}</FormMessage>
        </FormField>

        <FormField label="Description">
          <textarea
            {...register("description")}
            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
            rows={3}
            placeholder="Appointment description"
          />
          <FormMessage>{errors.description?.message}</FormMessage>
        </FormField>

        {/* <FormField label="Reason">
          <Input {...register("reason")} placeholder="Reason for appointment" />
          <FormMessage>{errors.reason?.message}</FormMessage>
        </FormField>

        <FormField label="Notes">
          <textarea
            {...register("notes")}
            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
            rows={2}
            placeholder="Additional notes"
          />
          <FormMessage>{errors.notes?.message}</FormMessage>
        </FormField> */}
      </div>

      {/* Recurring Appointment Options */}
      {/* <div className="space-y-4">
        <FormField label="">
          <label className="flex items-center space-x-2">
            <input
              {...register("isRecurring")}
              type="checkbox"
              className="form-checkbox h-4 w-4 text-indigo-600"
            />
            <span className="text-sm text-gray-700">Recurring Appointment</span>
          </label>
        </FormField>

        {watchIsRecurring && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Recurring Pattern" required>
              <Select {...register("recurringPattern")}>
                {recurringPatternOptions.map((pattern) => (
                  <option key={pattern} value={pattern}>
                    {pattern}
                  </option>
                ))}
              </Select>
              <FormMessage>{errors.recurringPattern?.message}</FormMessage>
            </FormField>

            <FormField label="End Date" required>
              <Calendar {...register("recurringEndDate")} />
              <FormMessage>{errors.recurringEndDate?.message}</FormMessage>
            </FormField>
          </div>
        )}
      </div> */}

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        {onCancel && (
          <Button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          disabled={loading}
          className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
        >
          {loading ? "Saving..." : isEditMode ? "Update Appointment" : "Create Appointment"}
        </Button>
      </div>
    </form>
  );
};



