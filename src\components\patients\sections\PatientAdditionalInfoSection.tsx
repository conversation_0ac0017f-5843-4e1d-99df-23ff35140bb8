import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import { Calendar } from "../../../commonfields/Calendar";
import FormMessage from "../../../commonfields/FormMessage";
import { allowOnlyNumbersWithLimit } from "../../../inputhelpers/inputHelpers";
import { allowOnlyLettersWithSpecialChars,allowAlphaNumericWithPolicyChars, allowOnlyCoverageAmount } from "../../../inputhelpers/inputHelpers";

import {
    relationTypeOptions,
    billingTypeOptions,
    citizenshipOptions,
    religionOptions,
    casteOptions,
    occupationOptions,
    educationOptions,
    annualIncomeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    isEditMode: boolean;
    onFieldChange: (fieldPath: string) => (e: React.ChangeEvent<any>) => void;
    handleInformationSharingChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const PatientAdditionalInfoSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    isEditMode,
    onFieldChange,
    handleInformationSharingChange,
}) => {
    const addArrayItem = (key: keyof PatientRegistrationPayload, newItem = {}) => {
        setForm(prev => ({
            ...prev,
            [key]: Array.isArray(prev[key]) ? [...(prev[key] as any[]), newItem] : [newItem],
        }));
    };

    const removeArrayItem = (key: keyof PatientRegistrationPayload, index: number) => {
        setForm(prev => ({
            ...prev,
            [key]: Array.isArray(prev[key])
                ? (prev[key] as any[]).filter((_, i) => i !== index)
                : prev[key],
        }));
    };

    return (
        <div className="space-y-2">
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Emergency Contacts</h3>
                        </div>
                        {
                            (form.emergencyContacts?.length || 0) < 2 && (
                                <button
                                    type="button"
                                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                                    onClick={() => addArrayItem("emergencyContacts")}
                                >
                                    + Add Contact
                                </button>
                            )
                        }
                    </div>
                </div>
                <div className="p-2 space-y-4">
                    {form.emergencyContacts?.map((contact, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-2 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                                <FormField label="Contact Name">
                                <Input
                                    name="contactName"
                                    value={contact.contactName || ""}
                                    onChange={onFieldChange(`emergencyContacts.${index}.contactName`)}
                                    onKeyDown={allowOnlyLettersWithSpecialChars(20)}
                                />
                                <FormMessage>{formErrors?.[`emergencyContacts.${index}.contactName`]}</FormMessage>
                            </FormField>
                                <FormField label="Relationship">
                                    <Select
                                        name="relationship"
                                        value={contact.relationship || ""}
                                        onChange={onFieldChange(`emergencyContacts.${index}.relationship`)}
                                    >
                                        <option value="">Select Relationship</option>
                                        {relationTypeOptions.map(rel => (
                                            <option key={rel} value={rel}>{rel}</option>
                                        ))}
                                    </Select>
                                    <FormMessage>{formErrors?.[`emergencyContacts.${index}.relationship`]}</FormMessage>
                                </FormField>
                                <FormField label="Phone Number">
                                    <Input
                                        name="phoneNumber"
                                        value={contact.phoneNumber || ""}
                                        onChange={onFieldChange(`emergencyContacts.${index}.phoneNumber`)}
                                        onKeyDown={allowOnlyNumbersWithLimit(10)}
                                    />
                                    <FormMessage>{formErrors?.[`emergencyContacts.${index}.phoneNumber`]}</FormMessage>
                                </FormField>
                            </div>
                            <div className="flex justify-end mt-4">
                                <button
                                    type="button"
                                    className="inline-flex items-center px-2 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onClick={() => removeArrayItem("emergencyContacts", index)}
                                >
                                    Remove
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Information Sharing Consent</h3>
                    </div>
                </div>
                <div className="p-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                    {["shareWithSpouse", "shareWithChildren", "shareWithCaregiver", "shareWithOther"].map(field => (
                    <label key={field} className="flex items-center space-x-3">
                        <input
                            type="checkbox"
                            name={field}
                            checked={form.informationSharing?.[field] || false}
                            onChange={handleInformationSharingChange}
                        />
                        <span className="text-sm capitalize">{field.replace("shareWith", "Share With ")}</span>
                    </label>
                ))}
                </div>
            </div>

            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-violet-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Patient Socio-Demographic Information</h3>
                    </div>
                </div>
                <div className="p-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                    {[{
                    name: "citizenship",
                    label: "Citizenship",
                    options: citizenshipOptions,
                }, {
                    name: "religion",
                    label: "Religion",
                    options: religionOptions,
                }, {
                    name: "caste",
                    label: "Caste",
                    options: casteOptions,
                }, {
                    name: "occupation",
                    label: "Occupation",
                    options: occupationOptions,
                }, {
                    name: "education",
                    label: "Education",
                    options: educationOptions,
                }, {
                    name: "annualIncome",
                    label: "Annual Income",
                    options: annualIncomeOptions,
                }].map(({ name, label, options }) => (
                    <FormField key={name} label={label}>
                        <Select
                            name={name}
                            value={form[name] || ""}
                            onChange={onFieldChange(name)}
                        >
                            <option value="">Select {label}</option>
                            {options.map(opt => (
                                <option key={opt} value={opt}>{opt}</option>
                            ))}
                        </Select>
                    </FormField>
                ))}
                </div>
            </div>

            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-orange-50 to-amber-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-orange-500 to-amber-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Billing Referral</h3>
                    </div>
                </div>
                <div className="p-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                    <FormField label="Billing Type">
                    <Select
                        name="billingType"
                        value={form.billingReferral?.billingType || ""}
                        onChange={onFieldChange("billingReferral.billingType")}
                    >
                        <option value="">Select Billing Type</option>
                        {billingTypeOptions.map(bt => (
                            <option key={bt} value={bt}>{bt}</option>
                        ))}
                    </Select>
                </FormField>
                    <FormField label="Referred By">
                        <Input
                            name="referredBy"
                            value={form.billingReferral?.referredBy || ""}
                            onChange={onFieldChange("billingReferral.referredBy")}
                            onKeyDown={allowOnlyLettersWithSpecialChars(20)}
                        />
                    </FormField>
                </div>
            </div>

            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Insurance Information</h3>
                    </div>
                </div>
                <div className="p-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                    <FormField label="Insurance Provider">
                    <Input
                        name="insuranceProvider"
                        value={form.insurance?.insuranceProvider || ""}
                        onChange={onFieldChange("insurance.insuranceProvider")}
                        onKeyDown={allowOnlyLettersWithSpecialChars(20)}
                    />
                </FormField>
                    <FormField label="Policy Number">
                        <Input
                            name="policyNumber"
                            value={form.insurance?.policyNumber || ""}
                            onChange={onFieldChange("insurance.policyNumber")}
                            onKeyDown={allowAlphaNumericWithPolicyChars(15)}
                        />
                    </FormField>
                    <FormField label="Coverage Amount">
                        <Input
                            name="coverageAmount"
                            type="number"
                            value={form.insurance?.coverageAmount ?? ""}
                            onChange={onFieldChange("insurance.coverageAmount")}
                            onKeyDown={allowOnlyCoverageAmount(15)}
                        />
                    </FormField>
                    <FormField label="Policy Start Date">
                        <Calendar
                            name="policyStartDate"
                            value={form.insurance?.policyStartDate || ""}
                            onChange={onFieldChange("insurance.policyStartDate")}
                        />
                        <FormMessage>{formErrors?.["insurance.policyStartDate"]}</FormMessage>
                    </FormField>
                    <FormField label="Policy End Date">
                        <Calendar
                            name="policyEndDate"
                            value={form.insurance?.policyEndDate || ""}
                            onChange={onFieldChange("insurance.policyEndDate")}
                        />
                        <FormMessage>{formErrors?.["insurance.policyEndDate"]}</FormMessage>
                    </FormField>
                </div>
            </div>
        </div>
    );
};

// ======================================================================================================
//referrals
{/* <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
    <div className="bg-gradient-to-r from-teal-50 to-cyan-50 px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
                <div className="w-2 h-8 bg-gradient-to-b from-teal-500 to-cyan-600 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-800">Referrals</h3>
            </div>
            <button
                type="button"
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg hover:from-teal-600 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                onClick={() => addArrayItem("referrals")}
            >
                + Add Referral
            </button>
        </div>
    </div>

    <div className="p-6 space-y-4">
        {form.referrals?.map((referral, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-200">
<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <FormField label="From Facility ID">
                        <Input
                            name="fromFacilityId"
                            value={referral.fromFacilityId || ""}
                            onChange={onFieldChange(`referrals.${index}.fromFacilityId`)}
                        />
                        <FormMessage>{formErrors?.[`referrals.${index}.fromFacilityId`]}</FormMessage>
                    </FormField>
                    <FormField label="To Facility ID">
                        <Input
                            name="toFacilityId"
                            value={referral.toFacilityId || ""}
                            onChange={onFieldChange(`referrals.${index}.toFacilityId`)}
                        />
                        <FormMessage>{formErrors?.[`referrals.${index}.toFacilityId`]}</FormMessage>
                    </FormField>
                    <FormField label="Referral Date">
                        <Calendar
                            name="referralDate"
                            value={referral.referralDate || ""}
                            onChange={onFieldChange(`referrals.${index}.referralDate`)}
                        />
                        <FormMessage>{formErrors?.[`referrals.${index}.referralDate`]}</FormMessage>
                    </FormField>
                    <FormField label="Reason">
                        <Input
                            name="reason"
                            value={referral.reason || ""}
                            onChange={onFieldChange(`referrals.${index}.reason`)}
                        />
                        <FormMessage>{formErrors?.[`referrals.${index}.reason`]}</FormMessage>
                    </FormField>
                </div>
                <div className="flex justify-end mt-4">
                    <button
                        type="button"
                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500"
                        onClick={() => removeArrayItem("referrals", index)}
                    >
                        Remove
                    </button>
                </div>
            </div>
        ))}
    </div>
</div> */}
