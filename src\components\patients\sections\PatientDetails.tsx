// PatientDetails.tsx
import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { X, User, RefreshCw, ArrowLeft } from 'lucide-react';
import { toast } from 'react-toastify';

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;


interface Contact {
  phoneNumber?: string;
  mobileNumber?: string;
  email?:string;
}


export interface Address {
  addressType: string;
  houseNoOrFlatNo?: string;
  address1?: string;
  address2?: string;
  cityOrVillage?: string;
  districtId?: string;
  stateId?: string;
  stateProvince?: string;
  postalCode?: string;
  country?: string;
}

interface Insurance {
  coverageAmount?: number;
  provider?: string;
  policyNumber?: string;
}

interface Patient {
  fullName?: string;
  gender?: string;
  dateOfBirth?: string;
  age?: number;
  registrationDate?: string;
  identifierType?: string;
  identifierNumber?: string;
  facilityId?: string;
  contacts?: Contact[];
  addresses?: Address[];
  insurance?: Insurance;
  upId?: string;
}

const isValidValue = (value: any): boolean => {
  if (
    value === null ||
    value === undefined ||
    value === '' ||
    value === 'string' ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
  ) {
    return false;
  }
  return true;
};


const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return dateString;
  }
};

const FieldRow = ({ 
  label, 
  value, 
  isDate = false 
}: { 
  label: string; 
  value?: string | number | boolean;
  isDate?: boolean;
}) => {
  if (!isValidValue(value)) return null;
  
  const displayValue = isDate && typeof value === 'string' 
    ? formatDate(value) 
    : String(value);

  return (
    <div className="text-sm space-x-1 break-words">
      <span className="font-semibold text-gray-600">{label}:</span>
      <span className="text-gray-800">{displayValue}</span>
    </div>
  );
};

const Section = ({
  title,
  children,
  color = "from-gray-50 to-gray-100",
  bar = "from-gray-400 to-gray-600"
}: {
  title: string;
  children: React.ReactNode;
  color?: string;
  bar?: string;
}) => {
  const childrenArray = Array.isArray(children) ? children : [children];
  const visibleChildren = childrenArray.filter(child => child !== null);
  if (visibleChildren.length === 0) return null;

  return (
    <div className="mt-6 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      <div className={`bg-gradient-to-r ${color} px-6 py-4 border-b border-gray-200`}>
        <div className="flex items-center space-x-3">
          <div className={`w-2 h-8 bg-gradient-to-b ${bar} rounded-full`} />
          <h4 className="text-md font-semibold text-gray-800">{title}</h4>
        </div>
      </div>
      <div className="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {visibleChildren}
      </div>
    </div>
  );
};

const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="flex flex-col items-center space-y-4">
      <RefreshCw className="h-8 w-8 animate-spin text-indigo-600" />
      <p className="text-gray-600">Loading patient details...</p>
    </div>
  </div>
);

const ErrorState = ({ onRetry, patientId }: { onRetry: () => void; patientId?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center space-y-4">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
        <X className="h-8 w-8 text-red-600" />
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-800">Patient Not Found</h3>
        <p className="text-gray-600 mt-1">
          {patientId ? `No patient found with ID: ${patientId}` : 'Invalid patient ID'}
        </p>
      </div>
      <div className="flex space-x-3 justify-center">
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          Retry
        </button>
      </div>
    </div>
  </div>
);

export default function PatientDetails() {
const { patientId } = useParams(); // returns string | undefined

  const navigate = useNavigate();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const fetchPatientData = async () => {
    if (!patientId) {
      setError(true);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(false);
      
      const res = await fetch(
        `${apiBaseUrl}/api/patients?upId=${encodeURIComponent(patientId)}`
      );
      
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      
      const data = await res.json();
      console.log(data);
      
      if (Array.isArray(data) && data.length > 0) {
        setPatient(data[0]);
      } else {
        setError(true);
        toast.error('Patient not found in database');
      }
    } catch (err) {
      console.error('Error fetching patient:', err);
      setError(true);
      toast.error('Failed to load patient details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPatientData();
  }, [patientId]);

  const handleBack = () => {
    navigate('/list', { replace: true });
  };

  const handleRetry = () => {
    fetchPatientData();
  };

  if (loading) {
    return (
      <div className="p-4 max-w-6xl mx-auto">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="p-4 max-w-6xl mx-auto relative">
        <button
          onClick={handleBack}
          className="absolute top-4 right-4 text-gray-600 hover:text-red-600 transition-colors"
          aria-label="Close"
        >
          <X size={24} />
        </button>
        <ErrorState onRetry={handleRetry} patientId={patientId} />
      </div>
    );
  }

  return (
    <div className="p-4 max-w-6xl mx-auto relative">
      {/* Navigation buttons */}
      <div className="absolute top-4 right-4 flex space-x-2">
        <button
          onClick={() => navigate('/scanner')}
          className="text-gray-600 hover:text-indigo-600 transition-colors p-2 rounded-lg hover:bg-gray-100"
          aria-label="Scan Another"
          title="Scan Another QR Code"
        >
          <User size={24} />
        </button>
        <button
          onClick={handleBack}
          className="text-gray-600 hover:text-red-600 transition-colors p-2 rounded-lg hover:bg-gray-100"
          aria-label="Back to List"
          title="Back to Patient List"
        >
          <ArrowLeft size={24} />
        </button>
      </div>

      {/* Header */}
      <div className="mb-8 text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <User className="h-10 w-10 text-white" />
        </div>
        <h3 className="text-2xl font-bold text-gray-800">
          {patient.fullName || 'Patient Details'}
        </h3>
        <p className="text-sm text-gray-500">
          Patient ID: {patientId} • Last Updated: {new Date().toLocaleDateString()}
        </p>
      </div>

      {/* Basic Information */}
      <Section title="Basic Information" color="from-indigo-50 to-purple-50" bar="from-indigo-500 to-purple-600">
        <FieldRow label="Full Name" value={patient.fullName} />
        <FieldRow label="Gender" value={patient.gender} />
        <FieldRow label="Date of Birth" value={patient.dateOfBirth} isDate />
        <FieldRow label="Age" value={patient.age} />
        <FieldRow label="Registration Date" value={patient.registrationDate} isDate />
      </Section>

      {/* Identification */}
      <Section title="Identification" color="from-blue-50 to-sky-50" bar="from-blue-500 to-sky-600">
        <FieldRow label="Patient ID" value={patient.upId || patientId} />
        <FieldRow label="Identifier Type" value={patient.identifierType} />
        <FieldRow label="Identifier Number" value={patient.identifierNumber} />
        <FieldRow label="Facility ID" value={patient.facilityId} />
      </Section>

      {/* Contact Information */}
      <Section title="Contact Information" color="from-green-50 to-emerald-50" bar="from-green-500 to-emerald-600">
        {patient.contacts && patient.contacts.length > 0 ? (
          patient.contacts.map((contact: Contact, index: number) => (
            <div key={index} className="contents">
              <FieldRow label="Phone Number" value={contact.phoneNumber} />
              <FieldRow label="Mobile Number" value={contact.mobileNumber} />
               <FieldRow label="Email" value={contact.email} />



            </div>
          ))
        ) : (
          <div className="col-span-full text-gray-500 text-sm italic">
            No contact information available
          </div>
        )}
      </Section>

      {/* Address Information */}
  <Section title="Address Information" color="from-yellow-50 to-amber-50" bar="from-yellow-500 to-amber-600">
  {patient.addresses && patient.addresses.length > 0 ? (
    patient.addresses.map((address: Address, index: number) => (
      <div key={index} className="contents">
        <FieldRow label="Address Type" value={address.addressType} />
        <FieldRow label="House/Flat No" value={address?.houseNoOrFlatNo} />
        <FieldRow label="Address Line 1" value={address.address1} />
        <FieldRow label="Address Line 2" value={address.address2} />
        <FieldRow label="City/Village" value={address.cityOrVillage} />
          <FieldRow label="State" value={address?.stateId} />
        <FieldRow label="District" value={address?.districtId} />
    
        <FieldRow label="Postal Code" value={address.postalCode} />
        <FieldRow label="Country" value={address.country} />
      </div>
    ))
  ) : (
    <div className="col-span-full text-gray-500 text-sm italic">
      No address information available
    </div>
  )}
</Section>


      {/* Insurance Details */}
      <Section title="Insurance Details" color="from-teal-50 to-cyan-50" bar="from-teal-500 to-cyan-600">
        {patient.insurance ? (
          <>
            <FieldRow label="Coverage Amount" value={patient.insurance.coverageAmount} />
            <FieldRow label="Insurance Provider" value={patient.insurance.provider} />
            <FieldRow label="Policy Number" value={patient.insurance.policyNumber} />
          </>
        ) : (
          <div className="col-span-full text-gray-500 text-sm italic">
            No insurance information available
          </div>
        )}
      </Section>

      {/* Action Buttons */}
      <div className="mt-8 flex justify-center space-x-4">
        <button
          onClick={() => navigate('/scanner')}
          className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
        >
          <User className="h-4 w-4" />
          <span>Scan Another Patient</span>
        </button>
        <button
          onClick={handleBack}
          className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to List</span>
        </button>
      </div>
      
    </div>
  );
}