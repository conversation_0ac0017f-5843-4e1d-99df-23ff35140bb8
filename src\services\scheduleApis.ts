import axios from "axios";
import type {
  DoctorSchedule,
  ScheduleConfigPayload,
  SlotConfigResponse,
  ScheduleFilters,
  ScheduleStats,
  ScheduleValidation
} from '../types/schedule';

// Doctor API Response Types
export interface DoctorSpecialization {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string;
  description: string;
  phoneNumber: string;
  email: string;
  location: string;
  operatingHours: Array<{
    id: number;
    dayOfWeek: string;
    isOperating: boolean;
    startTime: string;
    endTime: string;
    breakStartTime?: string;
    breakEndTime?: string;
    emergencyHours: boolean;
    createdAt: string;
    updatedAt: string;
  }>;
  isActive: boolean;
  isEmergencyDepartment: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Doctor {
  doctorId: string;
  consultantId?: string; // Add consultantId field if it exists in API response
  userId: string;
  specialization: DoctorSpecialization;
  fullName: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
  createdAt: string;
}

// Slot API Types
export interface ConsultantSlot {
  consultantId: string;
  slotDate: string; // YYYY-MM-DD format
  startTime: string; // HH:MM:SS.nnnnnnnnn format
  endTime: string; // HH:MM:SS.nnnnnnnnn format
  slotNumber: number;
  availability: 'OPEN' | 'BOOKED' | 'BLOCKED';
}

export interface SlotGenerationRequest {
  from: string; // YYYY-MM-DD format
  to: string; // YYYY-MM-DD format
}

export interface SlotGenerationResponse {
  slotsGenerated: number;
}

const BASE_URL = "https://megha-dev.sirobilt.com";

// Get All Doctors/Consultants
export const getDoctors = async (): Promise<{ success: boolean; data?: Doctor[]; error?: string }> => {
  try {
    console.log("Fetching doctors/consultants");

    const response = await axios.get("https://megha-dev.sirobilt.com/api/doctors");

    console.log("Doctors API response:", response.data);
    console.log("Sample doctor object from /api/doctors:", response.data?.[0]);

    // Log the structure to help identify consultantId field
    if (response.data?.[0]) {
      console.log("Doctor object keys from /api/doctors:", Object.keys(response.data[0]));
      console.log("Has consultantId?", 'consultantId' in response.data[0]);
      console.log("Has doctorId?", 'doctorId' in response.data[0]);
    }

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to fetch doctors:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch doctors"
    };
  }
};

// Get Available Doctors for a specific date
export const getAvailableDoctors = async (date?: string): Promise<{ success: boolean; data?: Doctor[]; error?: string }> => {
  try {
    // Use current date if no date provided, format as YYYY-MM-DD
    const targetDate = date || new Date().toISOString().split('T')[0];
    console.log("Fetching available doctors for date:", targetDate);

    const response = await axios.get(`https://megha-dev.sirobilt.com/api/doctors/available-doctors`, {
      params: { date: targetDate }
    });

    console.log("Available doctors API response:", response.data);
    console.log("Sample doctor object:", response.data?.[0]);

    // Log the structure to help identify consultantId field
    if (response.data?.[0]) {
      console.log("Doctor object keys:", Object.keys(response.data[0]));
      console.log("Has consultantId?", 'consultantId' in response.data[0]);
      console.log("Has doctorId?", 'doctorId' in response.data[0]);
    }

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to fetch available doctors:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch available doctors"
    };
  }
};

// Get Consultant Slots
export const getConsultantSlots = async (consultantId: string, date?: string): Promise<{ success: boolean; data?: ConsultantSlot[]; error?: string }> => {
  try {
    console.log("Fetching consultant slots:", { consultantId, date });

    let endpoint = `${BASE_URL}/api/consultants/${consultantId}/slots`;
    if (date) {
      endpoint += `?date=${date}`;
    }

    const response = await axios.get(endpoint);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to fetch consultant slots:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch consultant slots"
    };
  }
};

// Generate Consultant Slots
export const generateConsultantSlots = async (consultantId: string, request: SlotGenerationRequest): Promise<{ success: boolean; data?: SlotGenerationResponse; error?: string }> => {
  try {
    console.log("Generating consultant slots:", { consultantId, request });

    const response = await axios.post(`${BASE_URL}/api/consultants/${consultantId}/slots/generate`, request);

    return {
      success: true,
      data: { slotsGenerated: response.data }
    };
  } catch (error: any) {
    console.error("Failed to generate consultant slots:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to generate consultant slots"
    };
  }
};

// Create Doctor Schedule Configuration
export const createScheduleConfig = async (payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Creating schedule configuration:", payload);

    // Use new slot-configs endpoint
    const response = await axios.post(`${BASE_URL}/api/consultants/${payload.consultantId}/slot-configs`, payload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to create schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to create schedule configuration"
    };
  }
};

// Get All Schedule Configurations
export const getScheduleConfigs = async (filters: ScheduleFilters = {}): Promise<{
  results: DoctorSchedule[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}> => {
  try {
    console.log("Fetching schedule configurations with filters:", filters);

    // If a specific consultantId is provided, get that consultant's schedule
    if (filters.consultantId) {
      const endpoint = `${BASE_URL}/api/consultants/${filters.consultantId}/slot-configs`;
      const response = await axios.get(endpoint);
      const schedules = Array.isArray(response.data) ? response.data : [];

      return {
        results: schedules,
        totalElements: schedules.length,
        totalPages: 1,
        page: 0,
        size: schedules.length
      };
    }

    // For getting all schedules, we need to iterate through all doctors
    // First get all doctors
    const doctorsResponse = await getDoctors();
    if (!doctorsResponse.success || !doctorsResponse.data) {
      console.error("Failed to fetch doctors for schedule configs");
      return {
        results: [],
        totalElements: 0,
        totalPages: 0,
        page: 0,
        size: 20
      };
    }

    // Then get schedule configs for each doctor
    const allSchedules: DoctorSchedule[] = [];
    for (const doctor of doctorsResponse.data) {
      try {
        // Use consultantId if available, otherwise fall back to doctorId
        const consultantId = doctor.consultantId || doctor.doctorId;
        const endpoint = `${BASE_URL}/api/consultants/${consultantId}/slot-configs`;
        const response = await axios.get(endpoint);
        const schedules = Array.isArray(response.data) ? response.data : [];
        allSchedules.push(...schedules);
      } catch (error) {
        console.warn(`Failed to fetch schedule for doctor ${doctor.doctorId} (consultantId: ${doctor.consultantId || doctor.doctorId}):`, error);
        // Continue with other doctors even if one fails
      }
    }

    return {
      results: allSchedules,
      totalElements: allSchedules.length,
      totalPages: 1,
      page: 0,
      size: allSchedules.length
    };
  } catch (error: any) {
    console.error("Failed to fetch schedule configurations:", error);
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20
    };
  }
};

// Update Schedule Configuration
export const updateScheduleConfig = async (consultantId: string, payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Updating schedule configuration for consultant:", consultantId, payload);

    // Use new slot-configs endpoint with PUT method
    const response = await axios.put(`${BASE_URL}/api/consultants/${consultantId}/slot-configs`, payload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to update schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to update schedule configuration"
    };
  }
};

// Delete Schedule Configuration
export const deleteScheduleConfig = async (scheduleId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("Deleting schedule configuration:", scheduleId);

    await axios.delete(`${BASE_URL}/api/schedules/${scheduleId}`);

    return {
      success: true
    };
  } catch (error: any) {
    console.error("Failed to delete schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to delete schedule configuration"
    };
  }
};

// Generate Slots (Legacy function - now uses new consultant-specific API)
export const generateSlots = async (request: { from: string; to: string; providerId?: string }): Promise<{ success: boolean; message?: string; data?: any; error?: string }> => {
  try {
    console.log("Generating slots:", request);

    if (request.providerId) {
      // Use new consultant-specific API
      const result = await generateConsultantSlots(request.providerId, {
        from: request.from,
        to: request.to
      });

      if (result.success) {
        return {
          success: true,
          message: "Slots generated successfully",
          data: {
            totalSlotsGenerated: result.data?.slotsGenerated || 0,
            dateRange: { from: request.from, to: request.to },
            providersAffected: [request.providerId],
            conflicts: []
          }
        };
      } else {
        return {
          success: false,
          error: result.error || "Failed to generate slots"
        };
      }
    } else {
      // For all providers, we would need to iterate through all doctors
      // For now, return an error asking to select a specific doctor
      return {
        success: false,
        error: "Please select a specific doctor for slot generation"
      };
    }
  } catch (error: any) {
    console.error("Failed to generate slots:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to generate slots"
    };
  }
};

// Note: getAvailableSlots function removed - use getConsultantSlots instead
// This provides better integration with the new slot-configs API

// Get Schedule Statistics
export const getScheduleStats = async (): Promise<ScheduleStats> => {
  try {
    console.log("Fetching schedule statistics");



    const response = await axios.get(`${BASE_URL}/api/schedules/stats`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule statistics:", error);
    return {
      totalSchedules: 0,
      activeSchedules: 0,
      providersWithSchedules: 0,
      totalSlotsGenerated: 0,
      availableSlots: 0,
      bookedSlots: 0,
      upcomingScheduleChanges: 0
    };
  }
};

// Validate Schedule Configuration
export const validateScheduleConfig = async (payload: ScheduleConfigPayload): Promise<ScheduleValidation> => {
  try {
    console.log("Validating schedule configuration:", payload);

    const response = await axios.post(`${BASE_URL}/api/schedules/validate`, payload);

    return response.data;
  } catch (error: any) {
    console.error("Failed to validate schedule configuration:", error);
    return {
      isValid: false,
      errors: ["Validation failed"],
      warnings: [],
      conflicts: []
    };
  }
};

// Delete All Schedule Configurations for a Consultant
export const deleteConfigsByConsultantId = async (
  consultantId: string
): Promise<{ success: boolean; error?: string; status?: number }> => {
  try {
    console.log("Deleting all schedule configurations for consultant:", consultantId);

    const response = await axios.delete(`${BASE_URL}/api/slot-configs/consultant/${consultantId}`);

    if (response.status === 204) {
      return { success: true };
    } else {
      return {
        success: false,
        status: response.status,
        error: "Unexpected response from server"
      };
    }
  } catch (error: any) {
    console.error("Failed to delete schedule configurations by consultantId:", error);
    return {
      success: false,
      status: error?.response?.status,
      error: error?.response?.data?.message || "Failed to delete schedule configurations"
    };
  }
};

